// app/(components)/(content-layout)/user-management/deactivate/[id]/DeactivateUserPageClient.tsx
"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface DeactivateUserPageClientProps {
  userId: string;
}

/**
 * Deactivate User Page Client Component
 *
 * Client-side component that redirects to the user management page
 * with modal parameters for deactivate functionality.
 */
export default function DeactivateUserPageClient({ userId }: DeactivateUserPageClientProps) {
  const router = useRouter();

  useEffect(() => {
    // Redirect to user management page with modal parameters
    router.replace(`/user-management?modal=user-management&mode=deactivate&userId=${userId}`);
  }, [router, userId]);

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="flex items-center gap-3">
        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#E1B649]"></div>
        <span className="text-white">Loading...</span>
      </div>
    </div>
  );
}
