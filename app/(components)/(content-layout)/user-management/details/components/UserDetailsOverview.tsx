import React, { useMemo } from "react";
import Link from "next/link";
import { UserDetailsData } from "@/shared/types/user-management-types";
import { SpkBadge, SpkTable, SpkTableColumn, SpkLoadingSpinner, WalletTransactionModal } from "@/shared/UI/components";
import { formatSriLankanCurrency, formatUserDate } from "@/shared/utils/userDetailsUtils";
import { useUserDetails } from "@/shared/hooks";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";

interface UserDetailsOverviewProps {
	userData: UserDetailsData;
}

const UserDetailsOverview: React.FC<UserDetailsOverviewProps> = ({ userData }) => {
	// Use custom hook for all business logic
	const {
		betHistoryData: transactions,
		isBetHistoryLoading,
		isBetHistoryError,
		userTypeLabel,
		profileVerificationInfo,
		statusInfo,
		isWalletModalOpen,
		walletTransactionType,
		openWalletModal,
		closeWalletModal
	} = useUserDetails({ userId: userData.id });

	const InfoRow = ({ label, value, badge }: { label: string; value: string | number | boolean; badge?: React.ReactNode }) => (
		<div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
			<span className="text-sm font-medium text-gray-600 dark:text-gray-400">{label}</span>
			<div className="flex items-center gap-2">
				{badge && badge}
				<span className="text-sm text-gray-800 dark:text-white">
					{typeof value === "boolean" ? (value ? "Yes" : "No") : value}
				</span>
			</div>
		</div>
	);

	const WalletInfoRow = ({ label, value, icon, variant = "primary" }: {
		label: string;
		value: string;
		icon: string;
		variant?: "primary" | "success" | "info" | "warning" | "danger"
	}) => (
		<div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
			<div className="flex items-center gap-2">
				<i className={`${icon} text-${variant} text-sm`}></i>
				<span className="text-sm font-medium text-gray-600 dark:text-gray-400">{label}</span>
			</div>
			<span className="text-sm font-semibold text-gray-800 dark:text-white">
				{value}
			</span>
		</div>
	);

	// Wallet management functions
	const handleDepositClick = () => {
		openWalletModal("deposit");
	};

	const handleWithdrawClick = () => {
		openWalletModal("withdraw");
	};

	const handleWalletTransactionSuccess = (_transactionId: number) => {
		// The mutation hook will automatically invalidate queries and refresh data
	};

	// Bet history table columns - Updated for new API structure
	const betColumns: SpkTableColumn[] = useMemo(() => [
		{
			key: "transaction_id",
			title: "Transaction ID",
			width: "120px",
			render: (value) => (
				<span className="text-xs font-mono text-gray-600 dark:text-gray-400">
					{value ? `#${value.toString().slice(-8)}` : "N/A"}
				</span>
			)
		},
		{
			key: "amount",
			title: "Amount",
			width: "100px",
			render: (value) => (
				<span className="text-sm font-semibold text-gray-800 dark:text-white">
					{formatSriLankanCurrency(Number(value) || 0)}
				</span>
			)
		},
		{
			key: "transaction_type",
			title: "Type",
			width: "100px",
			render: (value) => {
				// Simplify transaction type display
				const getTypeDisplay = (type: string) => {
					if (type?.includes("credit")) return "Credit";
					if (type?.includes("debit")) return "Debit";
					if (type?.includes("bet")) return "Bet";
					if (type?.includes("win")) return "Win";
					return type?.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase()) || "N/A";
				};

				const getTypeVariant = (type: string) => {
					if (type?.includes("credit") || type?.includes("win")) return "success";
					if (type?.includes("debit") || type?.includes("bet")) return "primary";
					return "info";
				};

				return (
					<SpkBadge variant={getTypeVariant(value)} className="text-xs">
						{getTypeDisplay(value)}
					</SpkBadge>
				);
			}
		},
		{
			key: "status",
			title: "Status",
			width: "80px",
			render: (value) => {
				const getStatusVariant = (status: string) => {
					switch (status?.toLowerCase()) {
						case "success":
							return "success";
						case "pending":
							return "warning";
						case "cancelled":
						case "failed":
							return "danger";
						default:
							return "secondary";
					}
				};

				return (
					<SpkBadge variant={getStatusVariant(value)} className="text-xs">
						{value?.toUpperCase() || "N/A"}
					</SpkBadge>
				);
			}
		},
		{
			key: "created_at",
			title: "Date",
			width: "100px",
			render: (value) => (
				<span className="text-xs text-gray-600 dark:text-gray-400">
					{formatUserDate(value)}
				</span>
			)
		}
	], []);

	return (
		<>
			<div className="box h-full flex flex-col">
				<div className="box-header">
					<div className="box-title">
						<i className="ri-dashboard-line me-2"></i>
						User Overview
					</div>
				</div>
				<div className="box-body flex-1">
					{/* 3-Section Layout */}
					<div className="space-y-6">
						{/* Top Row - Basic Info and Wallet Info */}
						<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
							{/* Basic Info Section */}
							<div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
								<div className="flex items-center gap-2 mb-4">
									<i className="ri-user-line text-primary text-lg"></i>
									<h3 className="text-lg font-semibold text-gray-800 dark:text-white">Basic Information</h3>
								</div>
								<div className="space-y-1">
									<InfoRow label="Username" value={userData.userName} />
									<InfoRow label="First Name" value={userData.firstName || "N/A"} />
									<InfoRow label="Last Name" value={userData.lastName || "N/A"} />
									<InfoRow label="Email" value={userData.email} />
									<InfoRow
										label="Phone Number"
										value={userData.phoneCode ? `${userData.phoneCode} ${userData.phone}` : userData.phone}
									/>
									<InfoRow label="Gender" value={userData.gender || "Not specified"} />
									<InfoRow label="User Type" value={userTypeLabel} />
									<InfoRow
										label="Status"
										value={statusInfo.status}
										badge={<SpkBadge variant={statusInfo.variant}>{statusInfo.status}</SpkBadge>}
									/>
									<InfoRow
										label="Profile Verified"
										value={profileVerificationInfo.status}
										badge={<SpkBadge variant={profileVerificationInfo.variant}>{profileVerificationInfo.status}</SpkBadge>}
									/>
								</div>
							</div>

							{/* Wallet Info Section */}
							<div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
								<div className="flex items-center justify-between mb-4">
									<div className="flex items-center gap-2">
										<i className="ri-wallet-3-line text-success text-lg"></i>
										<h3 className="text-lg font-semibold text-gray-800 dark:text-white">Wallet Information</h3>
									</div>
									{/* Wallet Management Buttons */}
									<div className="flex gap-2">
										<SpkButton
											type="button"
											variant="primary"
											customClass="ti-btn ti-btn-sm ti-btn-success"
											onclickfunc={handleDepositClick}
											title="Deposit funds to user wallet"
										>
											<i className="ri-arrow-down-line me-1"></i>
											Deposit
										</SpkButton>
										<SpkButton
											type="button"
											variant="primary"
											customClass="ti-btn ti-btn-sm ti-btn-primary"
											onclickfunc={handleWithdrawClick}
											title="Withdraw funds from user wallet"
										>
											<i className="ri-arrow-up-line me-1"></i>
											Withdraw
										</SpkButton>
									</div>
								</div>
								<div className="space-y-1">
									<WalletInfoRow
										label="Current Balance"
										value={formatSriLankanCurrency(userData.amount)}
										icon="ri-money-dollar-circle-line"
										variant="success"
									/>
									{userData.nonCashAmount > 0 && (
										<WalletInfoRow
											label="Bonus Balance"
											value={formatSriLankanCurrency(userData.nonCashAmount)}
											icon="ri-gift-line"
											variant="info"
										/>
									)}
									<WalletInfoRow
										label="Currency"
										value={userData.currencycode || "LKR"}
										icon="ri-exchange-line"
										variant="primary"
									/>
									<WalletInfoRow
										label="Wallet Status"
										value={userData.active ? "Active" : "Inactive"}
										icon="ri-shield-check-line"
										variant={userData.active ? "success" : "danger"}
									/>
									<WalletInfoRow
										label="Last Updated"
										value={formatUserDate(userData.walletupdatedat)}
										icon="ri-time-line"
										variant="warning"
									/>
								</div>

								{/* Wallet Summary Card */}
								<div className="mt-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
									<div className="flex items-center gap-3">
										<div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center">
											<i className="ri-wallet-3-fill text-blue-600 dark:text-blue-300 text-lg"></i>
										</div>
										<div>
											<div className="text-lg font-bold text-blue-800 dark:text-blue-200">
												{formatSriLankanCurrency(userData.amount + (userData.nonCashAmount || 0))}
											</div>
											<div className="text-sm text-blue-600 dark:text-blue-300">
												Total Wallet Balance
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						{/* Bottom Row - Bet Info Section */}
						<div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
							<div className="flex items-center justify-between mb-4">
								<div className="flex items-center gap-2">
									<i className="ri-history-line text-warning text-lg"></i>
									<h3 className="text-lg font-semibold text-gray-800 dark:text-white">Recent Betting Activity</h3>
									{transactions.length > 0 && (
										<SpkBadge variant="primary" className="ml-2">
											{transactions.length}
										</SpkBadge>
									)}
								</div>
								<Link
									href={`/user-management/details/${userData.id}/bet-history`}
									className="ti-btn ti-btn-sm ti-btn-outline-primary"
								>
									<i className="ri-external-link-line me-1"></i>
									{transactions.length > 0 ? "View All" : "View Bet History"}
								</Link>
							</div>

							{/* Bet History Content */}
							{isBetHistoryLoading ? (
								<div className="flex items-center justify-center py-8">
									<SpkLoadingSpinner size="lg" />
								</div>
							) : isBetHistoryError ? (
								<div className="text-center py-8">
									<div className="w-12 h-12 mx-auto mb-3 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
										<i className="ri-error-warning-line text-xl text-red-500"></i>
									</div>
									<div className="text-sm text-red-600 dark:text-red-400">Failed to load bet history</div>
								</div>
							) : transactions.length === 0 ? (
								<div className="text-center py-8">
									<div className="w-12 h-12 mx-auto mb-3 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
										<i className="ri-history-line text-xl text-gray-400"></i>
									</div>
									<div className="text-sm text-gray-500">No bet history found</div>
									<div className="text-xs text-gray-400 mt-1">Recent betting transactions will appear here</div>
								</div>
							) : (
								<div className="overflow-x-auto">
									<SpkTable
										columns={betColumns}
										data={transactions}
										loading={false}
										hover={true}
										responsive={false}
										emptyText="No transactions found"
										className="bet-history-table"
										tableClass="table-auto w-full"
										size="sm"
									/>
								</div>
							)}
						</div>
					</div>
				</div>

			</div>
			<WalletTransactionModal
				isOpen={isWalletModalOpen}
				onClose={closeWalletModal}
				transactionType={walletTransactionType || undefined}
				preSelectedUser={{
					id: userData.id.toString(),
					userName: userData.userName,
					walletId: userData.walletid
				}}
				onSuccess={handleWalletTransactionSuccess}
				showUserSelection={false}
				context="user-details"
			/>
		</>
	);
};

export default UserDetailsOverview;
