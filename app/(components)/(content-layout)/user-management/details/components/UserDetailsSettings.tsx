import React from "react";
import { UserDetailsData } from "@/shared/types/user-management-types";
import { SpkBadge } from "@/shared/UI/components";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";

interface UserDetailsSettingsProps {
	userData: UserDetailsData;
}

const UserDetailsSettings: React.FC<UserDetailsSettingsProps> = ({ userData }) => {
	const formatDate = (dateString: string) => {
		if (!dateString) return "N/A";
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		});
	};

	const formatCurrency = (amount: string | number) => {
		const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: userData.currencycode.toUpperCase() === "CHIPS" ? "USD" : userData.currencycode.toUpperCase(),
			minimumFractionDigits: 2
		}).format(numAmount);
	};

	const getSettingIcon = (key: string) => {
		switch (key) {
		case "dailyBettingLimit":
		case "weeklyBettingLimit":
		case "monthlyBettingLimit":
			return "ri-gamepad-line";
		case "dailyDepositLimit":
			return "ri-money-dollar-circle-line";
		case "dailyWithdrawLimit":
			return "ri-bank-card-line";
		default:
			return "ri-settings-3-line";
		}
	};

	const getSettingLabel = (key: string) => {
		switch (key) {
		case "dailyBettingLimit":
			return "Daily Betting Limit";
		case "weeklyBettingLimit":
			return "Weekly Betting Limit";
		case "monthlyBettingLimit":
			return "Monthly Betting Limit";
		case "dailyDepositLimit":
			return "Daily Deposit Limit";
		case "dailyWithdrawLimit":
			return "Daily Withdraw Limit";
		default:
			return key.replace(/([A-Z])/g, " $1").replace(/^./, str => str.toUpperCase());
		}
	};

	const getSettingVariant = (key: string) => {
		if (key.includes("Betting")) return "primary";
		if (key.includes("Deposit")) return "success";
		if (key.includes("Withdraw")) return "warning";
		return "secondary";
	};

	return (
		<div className="box h-full flex flex-col">
			<div className="box-header">
				<div className="box-title">
					<i className="ri-settings-3-line me-2"></i>
					User Settings & Limits
				</div>
				<div className="ms-auto">
					<SpkButton
						type="button"
						customClass="ti-btn ti-btn-sm ti-btn-primary"
						title="Edit Settings"
					>
						<i className="ri-edit-line me-1"></i>
						Edit
					</SpkButton>
				</div>
			</div>
			<div className="box-body flex-1">
				{userData.setting && userData.setting.length > 0 ? (
					<div className="space-y-4">
						{userData.setting.map((setting) => (
							<div
								key={setting.id}
								className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
							>
								<div className="flex items-center gap-3">
									<div className={`w-10 h-10 rounded-full bg-${getSettingVariant(setting.key)}/10 flex items-center justify-center`}>
										<i className={`${getSettingIcon(setting.key)} text-${getSettingVariant(setting.key)} text-lg`}></i>
									</div>
									<div>
										<h6 className="text-sm font-semibold text-gray-800 dark:text-white mb-1">
											{getSettingLabel(setting.key)}
										</h6>
										<div className="text-xs text-gray-500">
											Updated: {formatDate(setting.updatedAt)}
										</div>
										{setting.description && (
											<div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
												{setting.description}
											</div>
										)}
									</div>
								</div>

								<div className="text-right">
									<div className="text-lg font-semibold text-gray-800 dark:text-white">
										{setting.key.toLowerCase().includes("limit") ?
											formatCurrency(setting.value) :
											setting.value
										}
									</div>
									<SpkBadge variant={getSettingVariant(setting.key)} className="text-xs">
										{getSettingVariant(setting.key).charAt(0).toUpperCase() + getSettingVariant(setting.key).slice(1)}
									</SpkBadge>
								</div>
							</div>
						))}
					</div>
				) : (
					<div className="text-center py-8">
						<div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
							<i className="ri-settings-3-line text-2xl text-gray-400"></i>
						</div>
						<h6 className="text-base font-medium text-gray-600 dark:text-gray-400 mb-2">
							No Settings Configured
						</h6>
						<p className="text-sm text-gray-500 mb-4">
							This user doesn't have any custom settings or limits configured yet.
						</p>
						<SpkButton
							type="button"
							customClass="ti-btn ti-btn-sm ti-btn-primary"
						>
							<i className="ri-add-line me-1"></i>
							Add Settings
						</SpkButton>
					</div>
				)}

				{/* Settings Summary */}
				{userData.setting && userData.setting.length > 0 && (
					<div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
						<h6 className="text-sm font-semibold text-gray-800 dark:text-white mb-3">
							Settings Summary
						</h6>
						<div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
							<div className="text-center p-3 bg-primary/10 rounded-lg">
								<div className="text-sm font-semibold text-primary">
									{userData.setting.filter(s => s.key.includes("Betting")).length}
								</div>
								<div className="text-xs text-gray-500">Betting Limits</div>
							</div>

							<div className="text-center p-3 bg-success/10 rounded-lg">
								<div className="text-sm font-semibold text-success">
									{userData.setting.filter(s => s.key.includes("Deposit")).length}
								</div>
								<div className="text-xs text-gray-500">Deposit Limits</div>
							</div>

							<div className="text-center p-3 bg-warning/10 rounded-lg">
								<div className="text-sm font-semibold text-warning">
									{userData.setting.filter(s => s.key.includes("Withdraw")).length}
								</div>
								<div className="text-xs text-gray-500">Withdraw Limits</div>
							</div>

							<div className="text-center p-3 bg-secondary/10 rounded-lg">
								<div className="text-sm font-semibold text-secondary">
									{userData.setting.length}
								</div>
								<div className="text-xs text-gray-500">Total Settings</div>
							</div>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

export default UserDetailsSettings;
