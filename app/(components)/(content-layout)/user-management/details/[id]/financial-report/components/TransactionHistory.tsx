import React, { useState, useMemo } from "react";
import { FinancialTransactionData } from "@/shared/types";
import { SpkTable, SpkTableColumn, SpkBadge, SpkFormSelect } from "@/shared/UI/components";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";

interface TransactionHistoryProps {
	transactions: FinancialTransactionData[];
	currency: string;
	isLoading?: boolean;
}

interface TransactionCardProps {
	transaction: FinancialTransactionData;
	currency: string;
}

const TransactionCard: React.FC<TransactionCardProps> = ({ transaction, currency }) => {
	const getTypeIcon = (type: string) => {
		const icons = {
			deposit: "ri-arrow-down-line text-success",
			withdrawal: "ri-arrow-up-line text-warning",
			cancellation: "ri-close-circle-line text-danger",
			bonus: "ri-gift-line text-info",
			commission: "ri-percent-line text-primary",
		};
		return icons[type as keyof typeof icons] || "ri-exchange-line text-gray-500";
	};

	const getStatusBadge = (status: string) => {
		const statusMap = {
			completed: { variant: "success" as const, text: "Completed" },
			pending: { variant: "warning" as const, text: "Pending" },
			cancelled: { variant: "danger" as const, text: "Cancelled" },
			failed: { variant: "danger" as const, text: "Failed" },
		};
		const statusInfo = statusMap[status as keyof typeof statusMap] || { variant: "secondary" as const, text: status };
		return <SpkBadge variant={statusInfo.variant}>{statusInfo.text}</SpkBadge>;
	};

	const formatAmount = (amount: number) => {
		return new Intl.NumberFormat("en-US", {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		}).format(amount);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		});
	};

	return (
		<div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow">
			<div className="flex items-start justify-between mb-3">
				<div className="flex items-center gap-3">
					<div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-full">
						<i className={`${getTypeIcon(transaction.type)} text-lg`}></i>
					</div>
					<div>
						<h6 className="font-semibold text-gray-800 dark:text-white capitalize">
							{transaction.type}
						</h6>
						<p className="text-sm text-gray-500 dark:text-gray-400">
							{transaction.transactionId}
						</p>
					</div>
				</div>
				<div className="text-right">
					<p className="text-lg font-bold text-gray-800 dark:text-white">
						{formatAmount(transaction.amount)} {currency}
					</p>
					{getStatusBadge(transaction.status)}
				</div>
			</div>

			{transaction.description && (
				<p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
					{transaction.description}
				</p>
			)}

			<div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
				<span>{formatDate(transaction.createdAt)}</span>
				{transaction.paymentMethod && (
					<span className="capitalize">{transaction.paymentMethod}</span>
				)}
			</div>
		</div>
	);
};

const TransactionHistory: React.FC<TransactionHistoryProps> = ({
	transactions,
	currency,
	isLoading = false
}) => {
	const [viewMode, setViewMode] = useState<"table" | "cards">("table");
	const [sortBy, setSortBy] = useState<"date" | "amount" | "type">("date");
	const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
	const [filterType, setFilterType] = useState<string>("all");
	const [filterStatus, setFilterStatus] = useState<string>("all");

	// Memoized filtered and sorted transactions
	const processedTransactions = useMemo(() => {
		// Ensure transactions is always an array
		const safeTransactions = Array.isArray(transactions) ? transactions : [];
		let filtered = [...safeTransactions];

		// Apply type filter
		if (filterType !== "all") {
			filtered = filtered.filter(t => t.type === filterType);
		}

		// Apply status filter
		if (filterStatus !== "all") {
			filtered = filtered.filter(t => t.status === filterStatus);
		}

		// Apply sorting
		filtered.sort((a, b) => {
			let comparison = 0;

			switch (sortBy) {
				case "date":
					comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
					break;
				case "amount":
					comparison = a.amount - b.amount;
					break;
				case "type":
					comparison = a.type.localeCompare(b.type);
					break;
			}

			return sortOrder === "asc" ? comparison : -comparison;
		});

		return filtered;
	}, [transactions, filterType, filterStatus, sortBy, sortOrder]);

	const formatAmount = (amount: number) => {
		return new Intl.NumberFormat("en-US", {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		}).format(amount);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		});
	};

	const getStatusBadge = (status: string) => {
		const statusMap = {
			completed: { variant: "success" as const, text: "Completed" },
			pending: { variant: "warning" as const, text: "Pending" },
			cancelled: { variant: "danger" as const, text: "Cancelled" },
			failed: { variant: "danger" as const, text: "Failed" },
		};
		const statusInfo = statusMap[status as keyof typeof statusMap] || { variant: "secondary" as const, text: status };
		return <SpkBadge variant={statusInfo.variant}>{statusInfo.text}</SpkBadge>;
	};

	const getTypeIcon = (type: string) => {
		const icons = {
			deposit: "ri-arrow-down-line text-success",
			withdrawal: "ri-arrow-up-line text-warning",
			cancellation: "ri-close-circle-line text-danger",
			bonus: "ri-gift-line text-info",
			commission: "ri-percent-line text-primary",
		};
		return icons[type as keyof typeof icons] || "ri-exchange-line text-gray-500";
	};

	// Table columns
	const columns: SpkTableColumn[] = useMemo(() => [
		{
			key: "type",
			title: "Type",
			render: (value, _record: FinancialTransactionData) => (
				<div className="flex items-center gap-2">
					<i className={getTypeIcon(value)}></i>
					<span className="capitalize font-medium">{value}</span>
				</div>
			)
		},
		{
			key: "transactionId",
			title: "Transaction ID",
			render: (value) => (
				<span className="font-mono text-sm text-gray-600 dark:text-gray-400">
					{value}
				</span>
			)
		},
		{
			key: "amount",
			title: "Amount",
			render: (value) => (
				<span className="font-semibold">
					{formatAmount(value)} {currency}
				</span>
			)
		},
		{
			key: "status",
			title: "Status",
			render: (value) => getStatusBadge(value)
		},
		{
			key: "createdAt",
			title: "Date",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{formatDate(value)}
				</span>
			)
		},
		{
			key: "paymentMethod",
			title: "Payment Method",
			render: (value) => (
				<span className="capitalize text-sm">
					{value || "N/A"}
				</span>
			)
		}
	], [currency]);

	if (isLoading) {
		return (
			<div className="bg-elevated rounded-lg border border-border-primary">
				<div className="bg-section px-4 py-3 border-b border-border-primary rounded-t-lg">
					<h5 className="text-text-primary font-medium">Transaction History</h5>
				</div>
				<div className="p-4">
					<div className="animate-pulse space-y-4">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="h-16 bg-[#333333] rounded"></div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="box">
			<div className="box-header">
				<div className="flex items-center justify-between w-full">
					<h5 className="box-title flex items-center gap-2">
						<div className="p-2 bg-primary/10 rounded-lg">
							<i className="ri-history-line text-primary text-lg"></i>
						</div>
						Transaction History
						{processedTransactions.length > 0 && (
							<SpkBadge variant="primary" className="ml-2">
								{processedTransactions.length}
							</SpkBadge>
						)}
					</h5>
					<div className="flex items-center gap-3">
						{/* Results Count */}
						{processedTransactions.length > 0 && (
							<div className="text-sm text-gray-500 dark:text-gray-400">
								{processedTransactions.length} transaction{processedTransactions.length !== 1 ? "s" : ""}
							</div>
						)}

						{/* View Mode Toggle */}
						<div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1 border border-gray-200 dark:border-gray-700">
							<button
								onClick={() => setViewMode("table")}
								className={`px-3 py-2 rounded text-sm font-medium transition-all duration-200 ${viewMode === "table"
									? "bg-white dark:bg-gray-700 text-primary shadow-sm border border-gray-200 dark:border-gray-600"
									: "text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
									}`}
								title="Table View"
							>
								<i className="ri-table-line me-1"></i>Table
							</button>
							<button
								onClick={() => setViewMode("cards")}
								className={`px-3 py-2 rounded text-sm font-medium transition-all duration-200 ${viewMode === "cards"
									? "bg-white dark:bg-gray-700 text-primary shadow-sm border border-gray-200 dark:border-gray-600"
									: "text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
									}`}
								title="Card View"
							>
								<i className="ri-layout-grid-line me-1"></i>Cards
							</button>
						</div>
					</div>
				</div>
			</div>

			{/* Filters */}
			<div className="box-body border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					<div className="space-y-2">
						<label className="text-sm font-medium text-gray-700 dark:text-gray-300">Transaction Type</label>
						<SpkFormSelect
							value={filterType}
							onChange={(e) => setFilterType(e.target.value)}
							options={[
								{ value: "all", label: "All Types" },
								{ value: "deposit", label: "Deposits" },
								{ value: "withdrawal", label: "Withdrawals" },
								{ value: "cancellation", label: "Cancellations" },
								{ value: "bonus", label: "Bonuses" },
								{ value: "commission", label: "Commissions" }
							]}
							placeholder="Select type..."
							className="w-full"
						/>
					</div>

					<div className="space-y-2">
						<label className="text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
						<SpkFormSelect
							value={filterStatus}
							onChange={(e) => setFilterStatus(e.target.value)}
							options={[
								{ value: "all", label: "All Status" },
								{ value: "completed", label: "Completed" },
								{ value: "pending", label: "Pending" },
								{ value: "cancelled", label: "Cancelled" },
								{ value: "failed", label: "Failed" }
							]}
							placeholder="Select status..."
							className="w-full"
						/>
					</div>

					<div className="space-y-2">
						<label className="text-sm font-medium text-gray-700 dark:text-gray-300">Sort By</label>
						<SpkFormSelect
							value={sortBy}
							onChange={(e) => setSortBy(e.target.value as "date" | "amount" | "type")}
							options={[
								{ value: "date", label: "Date" },
								{ value: "amount", label: "Amount" },
								{ value: "type", label: "Type" }
							]}
							placeholder="Sort by..."
							className="w-full"
						/>
					</div>

					<div className="space-y-2">
						<label className="text-sm font-medium text-gray-700 dark:text-gray-300">Sort Order</label>
						<div className="flex gap-2">
							<SpkButton
								type="button"
								customClass={`ti-btn flex-1 ${sortOrder === "asc" ? "ti-btn-primary" : "ti-btn-outline-primary"}`}
								onclickfunc={() => setSortOrder("asc")}
							>
								<i className="ri-sort-asc-line me-1"></i>
								Ascending
							</SpkButton>
							<SpkButton
								type="button"
								customClass={`ti-btn flex-1 ${sortOrder === "desc" ? "ti-btn-primary" : "ti-btn-outline-primary"}`}
								onclickfunc={() => setSortOrder("desc")}
							>
								<i className="ri-sort-desc-line me-1"></i>
								Descending
							</SpkButton>
						</div>
					</div>
				</div>

				{/* Active Filters Display */}
				{(filterType !== "all" || filterStatus !== "all") && (
					<div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
						<div className="flex items-center gap-2 flex-wrap">
							<span className="text-sm font-medium text-gray-700 dark:text-gray-300">Active Filters:</span>
							{filterType !== "all" && (
								<SpkBadge variant="primary" className="flex items-center gap-1">
									Type: {filterType}
									<button
										onClick={() => setFilterType("all")}
										className="ml-1 hover:bg-white/20 rounded-full p-0.5"
									>
										<i className="ri-close-line text-xs"></i>
									</button>
								</SpkBadge>
							)}
							{filterStatus !== "all" && (
								<SpkBadge variant="info" className="flex items-center gap-1">
									Status: {filterStatus}
									<button
										onClick={() => setFilterStatus("all")}
										className="ml-1 hover:bg-white/20 rounded-full p-0.5"
									>
										<i className="ri-close-line text-xs"></i>
									</button>
								</SpkBadge>
							)}
							<SpkButton
								type="button"
								customClass="ti-btn ti-btn-sm ti-btn-outline-secondary"
								onclickfunc={() => {
									setFilterType("all");
									setFilterStatus("all");
								}}
							>
								<i className="ri-close-line me-1"></i>
								Clear All
							</SpkButton>
						</div>
					</div>
				)}
			</div>

			<div className="box-body">
				{processedTransactions.length === 0 ? (
					<div className="text-center py-12">
						<div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
							<i className="ri-history-line text-2xl text-gray-400"></i>
						</div>
						<h6 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
							No Transactions Found
						</h6>
						<p className="text-gray-500 dark:text-gray-400">
							No transactions match your current filters.
						</p>
					</div>
				) : viewMode === "table" ? (
					<SpkTable
						columns={columns}
						data={processedTransactions}
						loading={isLoading}
						hover={true}
						responsive={true}
						emptyText="No transactions found"
						className="transaction-history-table"
					/>
				) : (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
						{processedTransactions.map((transaction) => (
							<TransactionCard
								key={transaction.id}
								transaction={transaction}
								currency={currency}
							/>
						))}
					</div>
				)}
			</div>
		</div>
	);
};

export default TransactionHistory;
