import { FinancialSummaryData } from "@/shared/types";
import { SpkLoadingSpinner, SummaryCard } from "@/shared/UI/components";
import React from "react";

interface FinancialSummaryProps {
	summaryData: FinancialSummaryData | undefined;
	currency: string;
	isLoading?: boolean;
}

// Note: Now using the reusable SummaryCard component from shared/UI/components
// This eliminates ~80 lines of duplicate card implementation code

const FinancialSummary: React.FC<FinancialSummaryProps> = ({
	summaryData,
	currency,
	isLoading = false
}) => {
	const summaryCards = [
		{
			title: "Total Deposits",
			amount: summaryData?.totalDeposits || 0,
			variant: "success" as const,
			icon: "ri-arrow-down-line",
		},
		{
			title: "Total Withdrawals",
			amount: summaryData?.totalWithdrawals || 0,
			variant: "warning" as const,
			icon: "ri-arrow-up-line",
		},
		{
			title: "Total Cancellations",
			amount: summaryData?.totalCancellations || 0,
			variant: "danger" as const,
			icon: "ri-close-circle-line",
		},
		{
			title: "Net Amount",
			amount: summaryData?.netAmount || 0,
			variant: "primary" as const,
			icon: "ri-wallet-line",
		},
	];

	return (
		<div className="box">
			<div className="box-header">
				<div className="flex items-center justify-between w-full">
					<h5 className="box-title flex items-center gap-2">
						<div className="p-2 bg-primary/10 rounded-lg">
							<i className="ri-bar-chart-line text-primary text-lg"></i>
						</div>
						Financial Summary
					</h5>
					{summaryData?.transactionCount && (
						<div className="flex items-center gap-2">
							<div className="text-sm text-gray-500 dark:text-gray-400">
								Based on {summaryData.transactionCount} transactions
							</div>
							{isLoading && (
								<SpkLoadingSpinner size="sm" inline={true} />
							)}
						</div>
					)}
				</div>
			</div>
			<div className="box-body">
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
					{summaryCards.map((card, index) => (
						<SummaryCard
							key={index}
							title={card.title}
							value={card.amount}
							currency={currency}
							variant={card.variant}
							icon={card.icon}
							isLoading={isLoading}
							backgroundType="cashier"
							size="md"
						/>
					))}
				</div>

				{/* Additional Summary Info */}
				{!isLoading && summaryData && (
					<div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
							<div>
								<p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Transaction Count</p>
								<p className="text-lg font-semibold text-gray-800 dark:text-white">
									{summaryData.transactionCount?.toLocaleString() || 0}
								</p>
							</div>
							<div>
								<p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Average Transaction</p>
								<p className="text-lg font-semibold text-gray-800 dark:text-white">
									{summaryData.transactionCount > 0
										? new Intl.NumberFormat("en-US", {
											minimumFractionDigits: 2,
											maximumFractionDigits: 2,
										}).format((summaryData.totalDeposits + summaryData.totalWithdrawals) / summaryData.transactionCount)
										: "0.00"
									} {currency}
								</p>
							</div>
							<div>
								<p className="text-sm text-gray-500 dark:text-gray-400 mb-1">Currency</p>
								<p className="text-lg font-semibold text-gray-800 dark:text-white">
									{currency}
								</p>
							</div>
						</div>
					</div>
				)}

				{/* Empty State */}
				{!isLoading && !summaryData && (
					<div className="text-center py-12">
						<div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
							<i className="ri-bar-chart-line text-2xl text-gray-400"></i>
						</div>
						<h6 className="text-lg font-semibold text-gray-800 dark:text-white mb-2">
							No Financial Data Available
						</h6>
						<p className="text-gray-500 dark:text-gray-400">
							No financial transactions found for this user.
						</p>
					</div>
				)}
			</div>
		</div>
	);
};

export default FinancialSummary;
