// app/(components)/(content-layout)/user-management/details/[id]/components/UserDetailsPageClient.tsx - Client-side component for user details
"use client";

import fadeInStyles from '@/app/css/animations/fade-in.module.css';
import { useUserDetails } from "@/shared/hooks";
import { useFinancialReportQuery } from "@/shared/query";
import { TabNavigation, UserProfileHeader, FinancialSummaryCard, CardSkeleton, SpkErrorMessage } from "@/shared/UI/components";
import { Fragment, useMemo, useState } from "react";
import UserAccountSummaryContent from "./UserAccountSummaryContent";
import UserLoginHistoryContent from "./UserLoginHistoryContent";

// Import extracted components
import { TabType } from "./UserDetailsTabNavigation";

// Import user-specific financial report component
import { UserStatisticsSection } from "@/shared/UI/user-management/UserStatisticsSection";
import UserFinancialReportContent from "./UserFinancialReportContent";

interface Props {
	userId: string;
}

/**
 * Client-side component that handles all interactive functionality for user details
 * Redesigned with 4 main sections as per specifications:
 * 1. User Profile Header (144px height)
 * 2. Statistics Cards (158px height)
 * 3. Tab Navigation (67px height)
 * 4. Tab Content Area
 */
export function UserDetailsPageClient({ userId }: Props) {
	// State for active tab
	const [activeTab, setActiveTab] = useState<TabType>('financial-report');

	// Use custom hook for all business logic
	const {
		userData,
		isLoading,
		isError,
		error,
		handleRefresh,
		isAuthenticated,
		hasHydrated,
		userTypeLabel,
		openWalletModal
	} = useUserDetails({ userId });

	// Fetch financial report data for the 30% summary cards section
	const {
		data: financialReportResponse
	} = useFinancialReportQuery({ playerId: userId });

	const summaryCardsData = useMemo(() => [
		{
			id: 'total-revenue',
			svgName: 'balance' as const,
			title: 'Total Revenue',
			value: financialReportResponse?.totalAmount || 0,
		},
		{
			id: 'net-profit',
			svgName: 'deposite' as const,
			title: 'Net Profit',
			value: (financialReportResponse?.totalDeposits || 0) - (financialReportResponse?.totalWithdrawals || 0),
		},
		{
			id: 'total-transactions',
			svgName: 'withdraw' as const,
			title: 'Transactions',
			value: financialReportResponse?.count || 0,
			currency: ''
		}
	], [financialReportResponse]);
	// Note: Bet report and login history data are now handled by their respective components

	// Don't render anything if not authenticated and hydrated
	if (hasHydrated && !isAuthenticated) {
		return null;
	}

	// Show loading state with skeleton
	if (isLoading) {
		return <CardSkeleton layout="single" showAvatar={true} showActions={true} showStats={true} />;
	}

	// Show error state
	if (isError || !userData) {
		return (
			<Fragment>
				<SpkErrorMessage
					message={error?.message || "Failed to load user details"}
					onRetry={handleRefresh}
					variant="box"
					size="md"
					title="Error Loading Data"
				/>
			</Fragment>
		);
	}

	return (
		<Fragment>
			{/* Main Content with 4 Sections Layout */}
			<div className={`flex flex-col gap-5 ${fadeInStyles.fadeIn}`}>
				{/* Section 1: User Profile Header (144px height) */}
				<UserProfileHeader
					userData={userData}
					userTypeLabel={userTypeLabel}
					showWalletBalance={true}
				/>

				{/* Section 2: Statistics Cards (158px height) */}
				<UserStatisticsSection
					userData={userData}
					onDepositClick={() => openWalletModal('deposit')}
					onWithdrawClick={() => openWalletModal('withdraw')}
				/>

				{/* Section 3: Tab Navigation (67px height) */}
				<TabNavigation
					tabs={[
						{ id: 'financial-report', label: 'Financial Report' },
						{ id: 'account-statement', label: 'Account Statement' },
						{ id: 'login-history', label: 'Login History' }
					]}
					activeTab={activeTab}
					onTabChange={(tab) => setActiveTab(tab as TabType)}
					variant="golden"
					radius={"16px"}
				/>

				{/* Section 4: Tab Content Area */}
				<div className="min-h-[400px]">
					{activeTab === 'financial-report' && (
						<div className="flex flex-col lg:flex-row gap-5 bg-elevated2 rounded-[16px] p-[12px] gap-[20px]">
							{/* 70% section - Financial Report Components */}
							<div className="flex-1 lg:flex-[0.75]">
								<UserFinancialReportContent userId={userId} />
							</div>

							{/* 30% section - Summary Cards */}
							<div className="flex-1 lg:flex-[0.25] flex flex-col gap-5">
								{summaryCardsData.map((cardData) => (
									<FinancialSummaryCard
										key={cardData.id}
										svgName={cardData.svgName}
										title={cardData.title}
										value={cardData.value}
										{...(cardData.currency !== undefined && { currency: cardData.currency })}
									/>
								))
								}
							</div>
						</div>
					)}

					{activeTab === 'account-statement' && (
						<div className="flex flex-col lg:flex-row gap-5 bg-elevated2 rounded-[16px] p-[12px] gap-[20px]">
							{/* Account Summary Content - using imported component */}
							<div className="flex-1">
								<UserAccountSummaryContent userId={userId} />
							</div>
						</div>
					)}

					{activeTab === 'login-history' && (
						<div className="flex flex-col lg:flex-row gap-5 bg-elevated2 rounded-[16px] p-[12px] gap-[20px]">
							{/* Login History Content - using imported component */}
							<div className="flex-1">
								<UserLoginHistoryContent userId={userId} />
							</div>
						</div>
					)}
				</div>
			</div>
		</Fragment>
	);
};
