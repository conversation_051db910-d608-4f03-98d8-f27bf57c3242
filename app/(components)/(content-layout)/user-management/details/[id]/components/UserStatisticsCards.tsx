// app/(components)/(content-layout)/user-management/details/[id]/components/UserStatisticsCards.tsx
import React from 'react';
import { PrimaryButton } from "@/shared/UI/components";
import { UserData } from '@/shared/types';

interface UserStatisticsCardsProps {
	userData: UserData;
	onDepositClick: () => void;
	onWithdrawClick: () => void;
}

const UserStatisticsCards: React.FC<UserStatisticsCardsProps> = ({
	userData,
	onDepositClick,
	onWithdrawClick
}) => {
	return (
		<div className="h-auto lg:h-[158px] flex flex-col lg:flex-row gap-3">
			{/* Card 1: Overall Deposit */}
			<div className="w-full lg:w-[427px] h-[158px] bg-section rounded-xl px-6 py-7 flex items-center justify-between gap-4">
				<div className="flex items-center gap-4">
					<div className="w-20 h-20 bg-blue-500 rounded-lg flex items-center justify-center">
						<i className="ri-arrow-down-line text-white text-2xl"></i>
					</div>
					<div className="flex flex-col gap-4">
						<span className="text-[#AEAEAE] font-rubik font-light text-base">Overall Deposit</span>
						<span className="text-white font-rubik font-semibold text-2xl">
							${userData?.lastdepositedamount?.toLocaleString() || '0'}
						</span>
					</div>
				</div>
				<PrimaryButton
					size="sm"
					onClick={onDepositClick}
				>
					Deposit
				</PrimaryButton>
			</div>

			{/* Card 2: Overall Withdraw */}
			<div className="w-full lg:w-[427px] h-[158px] bg-section rounded-xl px-6 py-7 flex items-center justify-between gap-4">
				<div className="flex items-center gap-4">
					<div className="w-20 h-20 bg-red-500 rounded-lg flex items-center justify-center">
						<i className="ri-arrow-up-line text-white text-2xl"></i>
					</div>
					<div className="flex flex-col gap-4">
						<span className="text-[#AEAEAE] font-rubik font-light text-base">Overall Withdraw</span>
						<span className="text-white font-rubik font-semibold text-2xl">
							${userData?.amount?.toLocaleString() || '0'}
						</span>
					</div>
				</div>
				<PrimaryButton
					size="sm"
					onClick={onWithdrawClick}
				>
					Withdraw
				</PrimaryButton>
			</div>

			{/* Card 3: Last Deposit/Withdraw */}
			<div className="w-full lg:w-[427px] h-[158px] bg-section rounded-xl px-6 py-7 flex gap-7">
				<div className="w-20 h-20 bg-green-500 rounded-lg flex items-center justify-center">
					<i className="ri-history-line text-white text-2xl"></i>
				</div>
				<div className="flex flex-col gap-4 flex-1">
					<div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-2">
						<span className="text-[#AEAEAE] font-rubik font-light text-base">Last Deposit</span>
						<span className="text-white font-rubik font-semibold text-xl">
							${userData?.lastdepositedamount?.toLocaleString() || '0'}
						</span>
						<span className="text-[#AEAEAE] font-rubik font-light text-base">
							{userData?.createdAt ? new Date(userData.createdAt).toLocaleDateString() : 'N/A'}
						</span>
					</div>
					<div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-2">
						<span className="text-[#AEAEAE] font-rubik font-light text-base">Last Withdraw</span>
						<span className="text-white font-rubik font-semibold text-xl">
							${userData?.amount?.toLocaleString() || '0'}
						</span>
						<span className="text-[#AEAEAE] font-rubik font-light text-base">
							{userData?.updatedAt ? new Date(userData.updatedAt).toLocaleDateString() : 'N/A'}
						</span>
					</div>
				</div>
			</div>

			{/* Card 4: Total Bets/Wins */}
			<div className="w-full lg:w-[427px] h-[158px] bg-section rounded-xl px-6 py-7 flex gap-7">
				<div className="w-20 h-20 bg-purple-500 rounded-lg flex items-center justify-center">
					<i className="ri-trophy-line text-white text-2xl"></i>
				</div>
				<div className="flex flex-col gap-4 flex-1">
					<div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-2">
						<span className="text-[#AEAEAE] font-rubik font-light text-xl">Total Bets</span>
						<span className="text-white font-rubik font-semibold text-3xl">
							${userData?.totalWageredAmount?.toLocaleString() || '0'}
						</span>
					</div>
					<div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-2">
						<span className="text-[#AEAEAE] font-rubik font-light text-xl">Total Wins</span>
						<span className="text-white font-rubik font-semibold text-3xl">
							${(userData?.amount && userData?.totalWageredAmount) ?
								(userData.amount - userData.totalWageredAmount).toLocaleString() : '0'}
						</span>
					</div>
				</div>
			</div>
		</div>
	);
};

export default UserStatisticsCards;
