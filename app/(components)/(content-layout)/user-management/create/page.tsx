// app/(components)/(content-layout)/user-management/create/page.tsx
"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';

/**
 * Create User Redirect Route
 *
 * This route handles direct navigation to /user-management/create
 * and redirects to the user management page with modal parameters.
 *
 * This maintains backward compatibility for bookmarks and direct links
 * while using the new global modal system.
 */
export default function CreateUserPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to user management page with modal parameters
    router.replace('/user-management?modal=user-management&mode=create');
  }, [router]);

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="flex items-center gap-3">
        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#E1B649]"></div>
        <span className="text-white">Loading...</span>
      </div>
    </div>
  );
}
