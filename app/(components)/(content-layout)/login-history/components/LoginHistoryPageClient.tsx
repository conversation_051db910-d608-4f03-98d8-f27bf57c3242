// app/(components)/(content-layout)/login-history/components/LoginHistoryPageClient.tsx - Client-side component for login history
"use client";

import fadeInStyles from '@/app/css/animations/fade-in.module.css';
import { useLoginHistory } from "@/shared/hooks";
import { LoginHistoryFilters, LoginHistoryResponse } from "@/shared/types/user-management-types";
import { Fragment, useCallback, useMemo } from "react";

// Import global components
import {
	GlobalDataTable,
	GlobalFilterSection,
	GlobalPageHeader
} from "@/shared/UI/components";
import { DEFAULT_LOGIN_HISTORY_VISIBLE_FILTERS, LOGIN_HISTORY_FILTERS } from "@/shared/config/loginHistoryFilters";

// Direct imports to avoid circular dependency issues
import { SpkErrorMessage } from "@/shared/UI/components";
import UserManagementSkeleton from "../../user-management/components/UserManagementSkeleton";

// Import the login history table columns logic
import { getLoginHistoryTableColumns } from "./LoginHistoryTableColumns";

interface LoginHistoryPageClientProps {
	initialLoginHistoryResponse?: LoginHistoryResponse | null;
	initialFilters?: LoginHistoryFilters;
}

/**
 * Client-side component that handles all interactive functionality for login history
 * Separated from the server component to maintain SSR SEO benefits
 * Uses custom hook for business logic separation and global components
 *
 * Features:
 * - Accepts server-side rendered initial data for performance
 * - Graceful fallback to client-side fetching
 * - Optimized re-rendering for pagination changes
 */
export function LoginHistoryPageClient({
	initialLoginHistoryResponse = null,
	initialFilters
}: LoginHistoryPageClientProps = {}) {
	// Use custom hook for all business logic with initial data
	const {
		filters,
		loginHistoryResponse,
		isLoading,
		isError,
		error,
		isFetching,
		totalRecords,
		handleFilterChange,
		handlePageChange,
		handleRefresh,
		isAuthenticated,
		hasHydrated
	} = useLoginHistory({
		initialLoginHistoryResponse,
		initialFilters
	});

	// Memoize table columns for performance
	const columns = useMemo(() => getLoginHistoryTableColumns(), []);

	// Handle items per page change - optimized with useCallback
	const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
		handleFilterChange({ limit: itemsPerPage.toString() });
	}, [handleFilterChange]);

	// Enhanced pagination handler
	const handlePageChangeWithTracking = useCallback((page: number) => {
		handlePageChange(page);
	}, [handlePageChange]);

	// Don't render anything if not authenticated and hydrated
	if (hasHydrated && !isAuthenticated) {
		return null;
	}

	// Show loading state with skeleton
	if (isLoading) {
		return <UserManagementSkeleton />;
	}

	return (
		<Fragment>
			{/* Global Page Header */}
			<GlobalPageHeader
				title="Login History"
			/>

			{/* Main Content with Enhanced Design */}
			<div className={`grid grid-cols-12 gap-6 ${fadeInStyles.fadeIn}`}>
				{/* Global Filter Section */}
				<div className="xl:col-span-12 col-span-12">
					<GlobalFilterSection
						filters={filters}
						onFilterChange={handleFilterChange}
						isLoading={isLoading || isFetching}
						availableFilters={LOGIN_HISTORY_FILTERS}
						defaultVisibleFilters={DEFAULT_LOGIN_HISTORY_VISIBLE_FILTERS}
						title="Filters"
					/>
				</div>

				{/* Enhanced Login History Table Section */}
				<div className="xl:col-span-12 col-span-12 transform transition-all duration-500 ease-in-out delay-100 rounded-[16px] overflow-visible relative">
					<div className="bg-filter p-[1rem] rounded-md">
						{isError ? (
							<SpkErrorMessage
								message={error?.message || "Failed to load login history"}
								onRetry={handleRefresh}
								variant="alert"
								size="md"
							/>
						) : (
							<GlobalDataTable
								columns={columns}
								data={loginHistoryResponse?.data || []}
								isLoading={isLoading}
								showPagination={true}
								currentPage={parseInt(filters.page)}
								totalItems={totalRecords}
								itemsPerPage={parseInt(filters.limit)}
								totalPages={loginHistoryResponse?.totalPages}
								onPageChange={handlePageChangeWithTracking}
								onItemsPerPageChange={handleItemsPerPageChange}
								emptyText="No login history found. Try adjusting your search filters."
								minHeight="400px"
							/>
						)}

					</div>
				</div>
			</div>
		</Fragment>
	);
}
