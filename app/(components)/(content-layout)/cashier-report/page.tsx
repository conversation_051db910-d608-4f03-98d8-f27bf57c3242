// app/(components)/(content-layout)/cashier-report/page.tsx
import { DEFAULT_CASHIER_REPORT_FILTERS } from '@/shared/types/report-types';
import { Metadata } from 'next';
import React from 'react';
import { CashierReportPageClient } from './components/CashierReportPageClient';

// Force dynamic rendering to avoid static generation issues with cookies
export const dynamic = 'force-dynamic';

// Generate metadata for the cashier report page (server-side)
export const metadata: Metadata = {
  title: 'Cashier Report | Admin Dashboard',
  description: 'View and manage cashier transactions and financial activities',
  keywords: 'cashier, transactions, financial, report, admin, dashboard',
  openGraph: {
    title: 'Cashier Report | Admin Dashboard',
    description: 'View and manage cashier transactions and financial activities',
    type: 'website',
  },
};

type Props = {};

/**
 * Dynamic cashier report page component
 * Handles SEO metadata generation and delegates client-side logic to CashierReportPageClient
 *
 * Features:
 * - Dynamic rendering to support authentication
 * - SEO optimization with structured data
 * - Client-side data fetching for authenticated users
 */
const CashierReportPage: React.FC<Props> = () => {
  return (
    <CashierReportPageClient
      initialCashierReportResponse={null}
      initialFilters={DEFAULT_CASHIER_REPORT_FILTERS}
    />
  );
};

export default CashierReportPage;
