// app/(components)/(authentication-layout)/layout.tsx
"use client";
import PrelineScript from "@/app/PrelineScript";
import { usePathname } from "next/navigation";
import React, { Fragment, useEffect, useRef, useState } from "react";

const Layout = ({ children }: any) => {

	const pathname = usePathname();
	const bodyRef = useRef<any>(null);
	const [isHydrated, setIsHydrated] = useState(false);

	// Handle hydration
	useEffect(() => {
		setIsHydrated(true);
	}, []);

	useEffect(() => {
		// Only manipulate DOM after hydration to prevent SSR/client mismatch
		if (isHydrated && typeof document !== "undefined") {
			bodyRef.current = document.body;
			// Add 'authentication-background' for authentication-related pages
			if (pathname.includes("/basic")) {
				bodyRef.current.classList.add("authentication-background");
			} else {
				bodyRef.current.classList.remove("authentication-background");
			}

			// Add 'coming-soon-main' for the coming soon page
			if (pathname.includes("/coming-soon") || pathname.includes("/under-maintainance")) {
				bodyRef.current.classList.add("coming-soon-main");
			} else {
				bodyRef.current.classList.remove("coming-soon-main");
			}
			if (pathname.includes("/sign-up/cover")) {
				bodyRef.current.classList.add("bg-white");
			} else {
				bodyRef.current.classList.remove("bg-white");
			}
		}

		// Clean up classes on route change
		return () => {
			if (bodyRef.current) {
				bodyRef.current.classList.remove("authentication-background");
				bodyRef.current.classList.remove("coming-soon-main");
				bodyRef.current.classList.remove("bg-white");
			}
		};
	}, [isHydrated, pathname]);

	return (
		<Fragment>
			{children}
			<PrelineScript />
		</Fragment>
	);

};

export default Layout;
