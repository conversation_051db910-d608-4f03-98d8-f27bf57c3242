// app/test-username-filter/page.tsx - Test page for username filter
"use client";

import React, { useState } from 'react';
import UserSearchFilter from '@/shared/UI/filters/UserSearchFilter';

interface UserInfo {
  username: string;
  playerId?: number;
  userID?: number;
}

export default function TestUsernameFilterPage() {
  const [selectedUser, setSelectedUser] = useState<UserInfo | null>(null);
  const [filterValue, setFilterValue] = useState('');

  const handleUserChange = (userInfo: UserInfo | null) => {
    setSelectedUser(userInfo);
    if (userInfo) {
      setFilterValue(userInfo.username);
    } else {
      setFilterValue('');
    }
  };

  const handleReset = () => {
    setSelectedUser(null);
    setFilterValue('');
  };

  return (
    <div className="p-6 bg-background min-h-screen">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Username Filter Test</h1>

        <div className="bg-elevated p-6 rounded-lg mb-6">
          <h2 className="text-lg font-semibold text-white mb-4">Test Typing</h2>
          <p className="text-gray-400 text-sm mb-4">
            Type in the username field below. It should NOT clear while you type.
          </p>

          <UserSearchFilter
            value={filterValue}
            onChange={handleUserChange}
            placeholder="Type here to test..."
            className="mb-4"
          />

          <button
            onClick={handleReset}
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/80"
          >
            Reset Filter
          </button>
        </div>

        {selectedUser && (
          <div className="bg-elevated p-6 rounded-lg mb-6">
            <h3 className="text-lg font-semibold text-white mb-4">Selected User</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Username:</span>
                <span className="text-white">{selectedUser.username}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Player ID:</span>
                <span className="text-white">{selectedUser.playerId || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">User ID:</span>
                <span className="text-white">{selectedUser.userID || 'N/A'}</span>
              </div>
            </div>
          </div>
        )}

        <div className="bg-section p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Test Instructions</h3>
          <ol className="text-gray-400 text-sm space-y-2 list-decimal list-inside">
            <li>Type at least 3 characters in the search field</li>
            <li>Verify that the input does NOT clear while typing</li>
            <li>Select a user from the dropdown if results appear</li>
            <li>Test the reset button to clear the filter</li>
            <li>Try typing again after reset</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
