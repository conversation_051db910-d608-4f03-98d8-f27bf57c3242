# CSS Module Architecture

This folder contains the modular CSS architecture for better maintainability and performance.

## Structure

- **animations/**: Dedicated animation files with reusable keyframes
  - `fade-in.module.css`: Fade-in animations
  - `slide-in.module.css`: Slide-in animations
  - `background-cycle.module.css`: Background cycling animations with dark overlay for text readability

- **components/**: Component-specific CSS modules
  - `signin.module.css`: SignIn/Login form styles
  - `performance.module.css`: Performance optimization styles

- **utilities/**: Utility CSS classes and helpers

## Usage

Import CSS modules in your components:

```tsx
import styles from '@/app/css/components/signin.module.css';
import fadeIn from '@/app/css/animations/fade-in.module.css';

// Use in component
<div className={`${styles.signinForm} ${fadeIn.fadeIn}`}>
```

## Benefits

- **Scoped Styling**: CSS modules provide automatic class name scoping
- **Tree Shaking**: Unused styles are eliminated during build
- **Better Performance**: Smaller bundle sizes and faster loading
- **Maintainability**: Clear separation of concerns and organized structure
