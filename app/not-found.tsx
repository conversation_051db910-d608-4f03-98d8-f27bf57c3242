import Link from "next/link";

export default function NotFound() {
	return (
		<div
			className="flex flex-col items-center justify-center min-h-screen bg-background text-white"
			suppressHydrationWarning={true}
		>
			<div
				className="text-center space-y-6"
				suppressHydrationWarning={true}
			>
				<h1 className="text-4xl font-bold text-white font-rubik">404 - Page Not Found</h1>
				<p className="text-lg text-gray-300">The page you're looking for doesn't exist or has been moved.</p>
				<Link
					scroll={false}
					href="/dashboard"
					className="inline-block px-6 py-3 bg-gradient-to-r from-[#E1B649] to-[#8A5911] text-black font-medium rounded-lg hover:opacity-90 transition-opacity font-rubik"
				>
					Return Home
				</Link>
			</div>
		</div>
	);
}
