// app/layout.tsx - Root layout with comprehensive SEO optimization
import React from "react";
import type { Metadata, Viewport } from "next";
import "./globals.scss";
import ClientProviders from "./Clientprovider";
import { poppins, rubik } from "@/shared/config/fonts";
import { defaultSEOConfig } from "@/shared/seo";
import {
	OrganizationStructuredData,
	WebsiteStructuredData
} from "@/shared/seo/components/StructuredData";

// Generate metadata for the root layout
export const metadata: Metadata = defaultSEOConfig;

// Generate viewport configuration
export const viewport: Viewport = {
	width: "device-width",
	initialScale: 1,
	maximumScale: 5,
	themeColor: "#0066cc",
	colorScheme: "light dark",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html lang="en" className={`${poppins.variable} ${rubik.variable}`}>
			<head>
				{/* Global structured data */}
				<OrganizationStructuredData
					name="Xintra"
					url="https://xintra.com"
					logo="https://xintra.com/assets/images/logo.png"
					description="Advanced user management and analytics platform for modern businesses"
					contactPoint={[
						{
							contactType: "customer service",
							email: "<EMAIL>"
						}
					]}
					sameAs={[
						"https://twitter.com/xintra",
						"https://linkedin.com/company/xintra",
						"https://facebook.com/xintra"
					]}
				/>
				<WebsiteStructuredData
					name="Xintra"
					url="https://xintra.com"
					description="Advanced user management and analytics platform for modern businesses"
					searchUrl="https://xintra.com/search?q={search_term_string}"
				/>
			</head>
			<body suppressHydrationWarning={true}>
				<ClientProviders>
					{children}
				</ClientProviders>
			</body>
		</html>
	);
}
