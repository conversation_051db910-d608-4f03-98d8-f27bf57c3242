# WebSocket Configuration Guide

## Overview

The TurboStars WebSocket integration provides real-time bet notifications from the sportsbook. This document explains how to configure and troubleshoot the WebSocket connection.

## Configuration

### Environment Variables

The WebSocket connection can be configured using environment variables in your `.env.local` file:

```bash
# WebSocket server URL (optional)
NEXT_PUBLIC_WEBSOCKET_URL=wss://your-websocket-server.com

# Admin backend URL (used as fallback for WebSocket)
NEXT_PUBLIC_ADMIN_BACKEND_URL=https://adminapi.ingrandstation.com
```

### Fallback Behavior

The WebSocket connection uses the following priority order for determining the server URL:

1. `NEXT_PUBLIC_WEBSOCKET_URL` (if set)
2. `NEXT_PUBLIC_ADMIN_BACKEND_URL` (if set)
3. `https://api.ingrandstation.com` (hardcoded fallback)

## Error Handling

The WebSocket implementation includes robust error handling:

### Connection Failures

- **Error 1006**: Connection failed - server may not be available
- **Error 1002**: Protocol error
- **Error 1003**: Unsupported data type

### Graceful Degradation

When the WebSocket server is not available:

- The application continues to function normally
- WebSocket status shows "Offline" instead of "Error"
- Console shows informational messages instead of errors
- No reconnection attempts are made for certain error codes

## Development vs Production

### Development

- WebSocket failures are treated as informational
- The app works without WebSocket connectivity
- Debug information is available in the console

### Production

To enable WebSocket in production:

1. Set up a WebSocket server that supports the TurboStars protocol
2. Configure `NEXT_PUBLIC_WEBSOCKET_URL` in your environment
3. Ensure the server supports the following endpoint: `/ws`
4. Implement proper authentication and channel subscription

## Troubleshooting

### Common Issues

1. **"WebSocket server not available"**
   - This is expected when no WebSocket server is configured
   - The app will continue to work without real-time notifications

2. **Connection keeps failing**
   - Check if the WebSocket server is running
   - Verify the URL is correct
   - Check network connectivity

3. **Authentication errors**
   - Verify the JWT token is valid and not expired
   - Check user permissions for WebSocket access

### Debug Information

Enable debug mode by checking the browser console for:
- Connection attempts
- Error messages
- Reconnection attempts
- Message handling

## WebSocket Protocol

### Connection URL Format

```
wss://server.com/ws?channel=CASHIER_TURBO_BET_PLACED&userId=12345&token=jwt-token
```

### Message Format

```json
{
  "type": "CASHIER_TURBO_BET_PLACED",
  "data": {
    "userId": 12345,
    "marketId": "market-123",
    "transactionId": "tx-456",
    "provider": "turbostars"
  },
  "timestamp": "2025-06-27T20:00:00Z"
}
```

## Future Enhancements

- Dynamic user authentication from auth context
- Environment-specific configuration
- Enhanced error monitoring
- Connection health checks
