# Report Data Mapping Documentation

This document explains how the API responses are mapped to the required table columns for each report type.

## Financial Report

### Required Table Columns:
- Username
- Timestamp
- Action Type
- Amount
- Description
- Transaction ID
- Total Balance
- Status

### API Response Mapping:
```typescript
{
  userName: row.from_wallet_uname || row.action_by_uname || 'Unknown',
  createdAt: row.created_at, // Timestamp
  actionType: getActionTypeFromTransactionType(row.transaction_type), // Action Type
  amount: parseFloat(row.amount) || 0, // Amount
  description: generateDescriptionFromMetaData(row.meta_data) || row.comments || 'No description', // Description
  transactionId: row.transaction_id, // Transaction ID
  totalBalance: parseFloat(row.ending_balance) || 0, // Total Balance
  status: row.status || 'unknown' // Status
}
```

### Transaction Type Mapping:
- 21: 'bet'
- 22: 'win'
- 1: 'deposit'
- 2: 'withdrawal'
- 3: 'transfer'

## Cashier Report

### Required Table Columns:
- Username
- Action Type
- Market ID
- Market Name
- Amount
- Transaction Timestamp
- Transaction / Bet ID
- Description
- Status

### API Response Mapping:
```typescript
{
  userName: row.from_wallet_uname || row.action_by_uname || 'Unknown',
  actionType: getActionTypeFromTransactionType(row.transaction_type), // Action Type
  marketId: extractMarketInfo(row.meta_data).marketId, // Market ID
  marketName: extractMarketInfo(row.meta_data).marketName, // Market Name
  amount: parseFloat(row.amount) || 0, // Amount
  createdAt: row.created_at, // Transaction Timestamp
  transactionId: row.transaction_id, // Transaction / Bet ID
  description: generateDescriptionFromMetaData(row.meta_data) || row.comments || 'No description', // Description
  status: row.status || 'unknown' // Status
}
```

## Bet Report

### Required Table Columns:
- Username
- Date & Time
- Market ID
- Bet ID
- Market Name
- Bet Type
- Odds
- Stake Amount
- Win Amount
- Status Settlement

### API Response Mapping:
```typescript
{
  userName: bet.userName, // Username
  createdAt: bet.createdAt, // Date & Time
  marketId: extractMarketInfoFromBetList(bet.betList).marketId, // Market ID
  betId: bet.betId, // Bet ID
  marketName: extractMarketInfoFromBetList(bet.betList).marketName, // Market Name
  betType: bet.betType || 'unknown', // Bet Type
  odds: calculateCombinedOdds(bet.betList), // Odds (combined for combo bets)
  betAmount: parseFloat(bet.betAmount) || 0, // Stake Amount
  winAmount: parseFloat(bet.winAmount) || 0, // Win Amount
  status: bet.status || 'unknown' // Status Settlement
}
```

## Helper Functions

### getActionTypeFromTransactionType(transactionType: number)
Maps numeric transaction types to readable action types.

### generateDescriptionFromMetaData(metaData: any[])
Creates a description from bet meta data by combining event names, markets, and outcomes.

### extractMarketInfo(metaData: any[])
Extracts market ID and market name from the first item in the meta_data array.

### extractMarketInfoFromBetList(betList: any[])
Extracts market ID and market name from the first item in the betList array.

### calculateCombinedOdds(betList: any[])
Calculates combined odds for combo bets by multiplying all individual odds together.

## API Endpoints

- **Financial Report**: `GET /api/v2/admin/transactions?actionCategory=financial&...`
- **Cashier Report**: `GET /api/v2/admin/transactions?...`
- **Bet Report**: `POST /api/v2/cashier/betWinReport`

## Notes

1. All reports use the same transaction API endpoint except for bet reports which use a dedicated endpoint.
2. The meta_data array contains detailed information about bets including events, markets, outcomes, and odds.
3. For combo bets, odds are calculated by multiplying all individual bet odds together.
4. Market information is extracted from the first item in meta_data or betList arrays.
5. Transaction types are mapped to human-readable action types using a predefined mapping.
