# Pre-commit Hooks Configuration

This project is configured with pre-commit hooks to enforce code quality standards before commits are allowed into the repository.

## What's Configured

### ESLint Pre-commit Hook
- **Tool**: `husky` + `lint-staged`
- **Purpose**: Automatically runs ESLint on staged files before each commit
- **Behavior**: Prevents commits if there are any linting errors or warnings

### Configuration Files

1. **`.husky/pre-commit`** - The pre-commit hook script
2. **`package.json`** - Contains lint-staged configuration
3. **`eslint.config.js`** - ESLint configuration (ESLint v9 format)

### Lint-staged Configuration

```json
{
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix --max-warnings 0"
    ]
  }
}
```

- **Files checked**: Only JavaScript/TypeScript files (`.js`, `.jsx`, `.ts`, `.tsx`)
- **Auto-fix**: ESLint will automatically fix issues it can
- **Zero tolerance**: `--max-warnings 0` means even warnings will prevent commits
- **Performance**: Only staged files are checked, not the entire codebase

## How It Works

1. **Developer stages files**: `git add <files>`
2. **Developer attempts commit**: `git commit -m "message"`
3. **Pre-commit hook runs**: Automatically triggered by husky
4. **Lint-staged executes**: Runs ESLint on only the staged files
5. **ESLint checks code**: Validates against project's linting rules
6. **Commit decision**:
   - ✅ **Success**: If no linting issues, commit proceeds
   - ❌ **Failure**: If linting issues found, commit is blocked

## Benefits

- **Code Quality**: Ensures consistent code style and catches potential issues
- **Performance**: Only checks changed files, not entire codebase
- **Developer Experience**: Auto-fixes many issues automatically
- **Team Consistency**: Enforces same standards for all team members
- **Early Detection**: Catches issues before they enter the repository

## What Happens When Linting Fails

If there are linting errors:
1. The commit is blocked
2. Clear error messages are displayed showing:
   - File path with issues
   - Line numbers
   - Specific rule violations
   - Error descriptions
3. Developer must fix the issues and try committing again

## Example Error Output

```
✖ eslint --fix --max-warnings 0:

/path/to/file.js
  3:7  error  'unusedVariable' is assigned a value but never used  no-unused-vars
  7:5  error  All 'var' declarations must be at the top of the function scope  vars-on-top

✖ 2 problems (2 errors, 0 warnings)

husky - pre-commit script failed (code 1)
```

## Bypassing Pre-commit Hooks (Not Recommended)

In emergency situations, you can bypass pre-commit hooks with:
```bash
git commit --no-verify -m "emergency commit"
```

**⚠️ Warning**: This should only be used in exceptional circumstances as it defeats the purpose of code quality enforcement.

## Troubleshooting

### Common Issues

1. **ESLint configuration errors**: Check `eslint.config.js` for syntax issues
2. **Module resolution issues**: Ensure all ESLint plugins are installed
3. **Performance issues**: lint-staged only checks staged files for better performance

### Updating Configuration

- **ESLint rules**: Modify `eslint.config.js`
- **File patterns**: Update `lint-staged` configuration in `package.json`
- **Pre-commit behavior**: Modify `.husky/pre-commit`

## Dependencies

- `husky`: Git hooks management
- `lint-staged`: Run commands on staged files
- `eslint`: JavaScript/TypeScript linting
- Various ESLint plugins and configurations

All dependencies are managed via pnpm and defined in `package.json`.
