# Development Performance Optimization

This document outlines the changes made to disable real-time ESLint and TypeScript checking during development while maintaining code quality gates at commit and build time.

## Overview

The development environment has been optimized to improve performance by disabling real-time linting and type checking that can slow down the development workflow. Code quality is still enforced at appropriate checkpoints:

- ✅ **Git commit time** - via husky pre-commit hooks
- ✅ **Build time** - via npm/pnpm build scripts
- ❌ **Development time** - disabled for better performance

## Changes Made

### 1. Next.js Configuration (`next.config.ts`)

```typescript
eslint: {
  ignoreDuringBuilds: false, // Enable ESLint validation during build
  // Disable ESLint during development for better performance
  dirs: process.env.NODE_ENV === 'development' ? [] : ['app', 'shared'],
},

typescript: {
  ignoreBuildErrors: false, // Enable TypeScript validation during build
  // Disable TypeScript checking during development for better performance
  // Type checking will still run during builds and commits
  tsconfigPath: process.env.NODE_ENV === 'development' ? './tsconfig.dev.json' : './tsconfig.json',
},
```

### 2. Development TypeScript Configuration (`tsconfig.dev.json`)

Created a development-specific TypeScript configuration that:
- Extends the main `tsconfig.json`
- Disables strict type checking (`strict: false`)
- Disables incremental compilation for faster startup
- Disables various strict checking options for development speed

### 3. VSCode Settings (`.vscode/settings.json`)

Created IDE settings to disable:
- ESLint auto-fixing and validation
- TypeScript error checking in the editor
- JavaScript validation
- Auto-save and format on save
- Real-time error squiggles
- Performance optimizations for file watching

### 4. Package.json Scripts

Updated scripts to ensure quality gates remain in place:

```json
{
  "dev": "NODE_ENV=development next dev",
  "dev:fast": "NODE_ENV=development next dev --turbo",
  "build": "npm run type-check && npm run lint && next build",
  "build:fast": "next build",
  "type-check": "tsc --noEmit",
  "type-check:watch": "tsc --noEmit --watch",
  "quality-check": "npm run type-check && npm run lint"
}
```

### 5. Enhanced Pre-commit Hooks (`package.json`)

Updated lint-staged configuration to include TypeScript checking:

```json
{
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix --max-warnings 0"
    ],
    "*.{ts,tsx}": [
      "tsc --noEmit --skipLibCheck"
    ]
  }
}
```

## Available Scripts

### Development Scripts
- `pnpm dev` - Start development server with optimized performance
- `pnpm dev:fast` - Start development server with Turbo mode
- `pnpm type-check:watch` - Run TypeScript checking in watch mode (optional)

### Quality Assurance Scripts
- `pnpm lint` - Run ESLint on the entire codebase
- `pnpm type-check` - Run TypeScript type checking
- `pnpm quality-check` - Run both linting and type checking
- `pnpm build` - Build with full quality checks (lint + type-check + build)
- `pnpm build:fast` - Build without pre-checks (faster, but less safe)

## Quality Gates

### 1. Pre-commit Hooks (Husky + Lint-staged)
- **When**: Every git commit
- **What**: ESLint with auto-fix + TypeScript type checking
- **Files**: Only staged files (performance optimized)
- **Behavior**: Blocks commit if errors found

### 2. Build Process
- **When**: `pnpm build` command
- **What**: Full TypeScript checking + ESLint + Next.js build
- **Files**: Entire codebase
- **Behavior**: Fails build if errors found

### 3. Manual Quality Checks
- **When**: On-demand via `pnpm quality-check`
- **What**: Full TypeScript checking + ESLint
- **Files**: Entire codebase
- **Use case**: Before pushing, during code review

## Performance Benefits

1. **Faster Development Server Startup** - No initial ESLint/TypeScript checking
2. **Faster File Change Processing** - No real-time validation on every save
3. **Reduced CPU Usage** - No background linting/type checking processes
4. **Improved IDE Responsiveness** - Disabled real-time error checking
5. **Faster Hot Reload** - Less processing on file changes

## Maintaining Code Quality

Despite disabled real-time checking, code quality is maintained through:

1. **Commit-time Validation** - Husky pre-commit hooks prevent bad code from being committed
2. **Build-time Validation** - CI/CD and production builds will fail if code quality issues exist
3. **Manual Validation** - Developers can run quality checks on-demand
4. **IDE Features** - Syntax highlighting and basic IntelliSense still work

## Reverting Changes

To re-enable real-time checking:

1. **Remove/rename** `.vscode/settings.json`
2. **Revert** `next.config.ts` ESLint and TypeScript configurations
3. **Use** `tsconfig.json` instead of `tsconfig.dev.json` for development
4. **Update** package.json dev script to remove `NODE_ENV=development`

## Best Practices

1. **Run quality checks before pushing**: `pnpm quality-check`
2. **Use meaningful commit messages** - pre-commit hooks will validate your code
3. **Test builds locally**: `pnpm build` before deploying
4. **Monitor CI/CD** - builds will catch any quality issues missed locally
5. **Use type-check watch mode** when working on complex TypeScript: `pnpm type-check:watch`

## Troubleshooting

### Pre-commit hooks not working
```bash
# Reinstall husky
pnpm run prepare
```

### TypeScript errors in production
```bash
# Run full type check
pnpm type-check
```

### ESLint errors in CI/CD
```bash
# Run full lint check
pnpm lint
```

### Development server issues
```bash
# Clear Next.js cache
rm -rf .next
pnpm dev
```
