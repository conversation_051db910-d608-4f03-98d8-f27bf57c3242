# Component Migration Plan

## 🎯 Overview

This document provides a detailed, step-by-step migration strategy for optimizing component architecture across the project. The plan is organized into phases with specific tasks, timelines, and success criteria.

## 📋 Migration Phases

### Phase 1: Foundation & Quick Wins (Immediate)
**Duration:** 1-2 days  
**Risk Level:** Low  
**Impact:** High  

#### 1.1 Remove Duplicate Table Columns
**Target:** Eliminate duplicate table column implementations

**Steps:**
1. Compare `shared/UI/components/tables/BetReportTableColumns.tsx` vs `shared/components/tables/BetReportTableColumns.tsx`
2. Keep the version in `shared/components/tables/` (established location)
3. Remove duplicate from `shared/UI/components/tables/`
4. Update imports in consuming components
5. Test table functionality

**Files Affected:**
- Remove: `shared/UI/components/tables/BetReportTableColumns.tsx`
- Update imports in: BetReportPageClient components

#### 1.2 Consolidate Error Messages
**Target:** Use shared SpkErrorMessage throughout

**Steps:**
1. Identify all custom ErrorMessage components
2. Replace with SpkErrorMessage imports
3. Remove custom wrapper components
4. Update props to match SpkErrorMessage interface

**Files Affected:**
- Remove: `app/(components)/(content-layout)/user-management/details/components/ErrorMessage.tsx`
- Update: All components using custom error messages

### Phase 2: UserDetailsPageClient Optimization (High Priority)
**Duration:** 2-3 days  
**Risk Level:** Medium  
**Impact:** High  

#### 2.1 Extract User Profile Header Component
**Target:** Create reusable UserProfileHeader component

**New Component:** `shared/UI/user-management/UserProfileHeader.tsx`

```tsx
interface UserProfileHeaderProps {
  userData: UserData;
  userTypeLabel: string;
  onWalletAction: () => void;
  className?: string;
}
```

**Steps:**
1. Extract lines 87-140 from UserDetailsPageClient
2. Create TypeScript interface
3. Implement component with proper styling
4. Add to shared/UI/user-management/
5. Update UserDetailsPageClient to use new component

#### 2.2 Extract Statistics Cards Section
**Target:** Create reusable StatisticsSection using existing StatisticsCard

**New Component:** `shared/UI/user-management/UserStatisticsSection.tsx`

```tsx
interface UserStatisticsSectionProps {
  financialData: FinancialSummaryData;
  isLoading?: boolean;
  className?: string;
}
```

**Steps:**
1. Extract lines 142-200 from UserDetailsPageClient
2. Use existing StatisticsCard components
3. Implement responsive grid layout
4. Add loading states
5. Update UserDetailsPageClient

#### 2.3 Extract Tab Navigation Component
**Target:** Create reusable TabNavigation component

**New Component:** `shared/UI/components/TabNavigation.tsx`

```tsx
interface TabNavigationProps<T extends string> {
  tabs: Array<{ id: T; label: string; icon?: string }>;
  activeTab: T;
  onTabChange: (tab: T) => void;
  className?: string;
}
```

**Steps:**
1. Extract tab navigation logic from UserDetailsPageClient
2. Create generic, reusable component
3. Support icons and custom styling
4. Implement accessibility features
5. Update UserDetailsPageClient

### Phase 3: Legacy Component Migration (Medium Priority)
**Duration:** 3-4 days  
**Risk Level:** Medium  
**Impact:** High  

#### 3.1 Migrate spk-cards to shared/UI/cards
**Target:** Replace all legacy card components

**Migration Map:**
- `spkgridcards.tsx` → `GridCard.tsx`
- `spkgridmarkupcard.tsx` → `GridCard.tsx`
- `spkbgcards.tsx` → `SummaryCard.tsx`

**Steps:**
1. Audit all usages of legacy card components
2. Create migration mapping for props
3. Update components one by one
4. Test visual consistency
5. Remove legacy components after migration

#### 3.2 Migrate Widget Components
**Target:** Replace widget components with StatisticsCard

**Migration Map:**
- `spk-widgetcard.tsx` → `StatisticsCard.tsx`
- `spk-widgetcard1.tsx` → `StatisticsCard.tsx`
- `spk-widgetcard2.tsx` → `StatisticsCard.tsx`

**Steps:**
1. Map widget props to StatisticsCard props
2. Update chart integration if needed
3. Migrate components systematically
4. Verify styling matches
5. Remove legacy widget components

### Phase 4: Skeleton Component Consolidation (Medium Priority)
**Duration:** 2-3 days  
**Risk Level:** Low  
**Impact:** Medium  

#### 4.1 Create Shared Skeleton Components
**Target:** Standardize loading states

**New Components:**
- `shared/UI/skeletons/TableSkeleton.tsx`
- `shared/UI/skeletons/CardSkeleton.tsx`
- `shared/UI/skeletons/PageSkeleton.tsx`
- `shared/UI/skeletons/FormSkeleton.tsx`

**Steps:**
1. Analyze existing skeleton patterns
2. Create flexible, configurable skeletons
3. Implement responsive behavior
4. Add dark theme support
5. Create index file for exports

#### 4.2 Migrate Existing Skeletons
**Target:** Replace custom skeletons with shared ones

**Steps:**
1. Replace UserManagementSkeleton with TableSkeleton
2. Replace FinancialReportSkeleton with TableSkeleton
3. Replace UserDetailsCardSkeleton with CardSkeleton
4. Update LazyRoute to use shared PageSkeleton
5. Remove old skeleton components

### Phase 5: PageClient Pattern Optimization (Lower Priority)
**Duration:** 4-5 days  
**Risk Level:** High  
**Impact:** High  

#### 5.1 Extract Common PageClient Patterns
**Target:** Create reusable report page components

**New Components:**
- `shared/UI/layouts/ReportPageLayout.tsx`
- `shared/UI/components/ReportFilters.tsx`
- `shared/UI/components/ReportTable.tsx`

**Steps:**
1. Analyze common patterns across PageClient components
2. Create flexible layout component
3. Standardize filter patterns
4. Create reusable table wrapper
5. Migrate one PageClient at a time

### Phase 6: Folder Structure Optimization (Maintenance)
**Duration:** 2-3 days  
**Risk Level:** Medium  
**Impact:** Medium  

#### 6.1 Reorganize Component Structure
**Target:** Consistent folder organization

**New Structure:**
```
shared/UI/
├── cards/                   # ✅ Already optimized
├── components/              # Global reusable components
├── forms/                   # Form-related components
├── headers/                 # Header components
├── modals/                  # Modal components
├── skeletons/              # Loading skeleton components
├── tables/                  # Table-related components
└── user-management/         # User-specific components
```

**Steps:**
1. Create new directory structure
2. Move components to appropriate directories
3. Update all import statements
4. Update index files
5. Test all imports work correctly

### Phase 7: Testing & Validation (Critical)
**Duration:** 2-3 days  
**Risk Level:** Low  
**Impact:** Critical  

#### 7.1 Comprehensive Testing
**Target:** Ensure no regressions

**Testing Areas:**
1. Visual regression testing
2. Functionality preservation
3. Performance monitoring
4. Accessibility compliance
5. Dark theme compatibility

**Steps:**
1. Create test checklist
2. Test each migrated component
3. Verify styling consistency
4. Check performance metrics
5. Document any issues found

## 📊 Migration Tracking

### Success Metrics
- [ ] 30% reduction in duplicate component code
- [ ] All legacy spk-cards migrated
- [ ] All duplicate table columns removed
- [ ] Shared skeleton components implemented
- [ ] UserDetailsPageClient optimized
- [ ] No visual regressions
- [ ] Performance maintained or improved

### Risk Mitigation
1. **Backup Strategy**: Keep old components until migration complete
2. **Gradual Migration**: One component type at a time
3. **Testing**: Comprehensive testing after each phase
4. **Rollback Plan**: Ability to revert changes if issues found

## 🛠️ Implementation Guidelines

### Code Quality Standards
1. **TypeScript**: Full type safety for all new components
2. **Dark Theme**: Semantic CSS classes for theme compatibility
3. **Accessibility**: ARIA labels and keyboard navigation
4. **Performance**: React.memo and useCallback where appropriate
5. **Documentation**: JSDoc comments for all public interfaces

### Testing Requirements
1. **Visual Testing**: Screenshot comparison for UI components
2. **Unit Testing**: Logic testing for complex components
3. **Integration Testing**: Component interaction testing
4. **Performance Testing**: Bundle size and render time monitoring

### Documentation Updates
1. **Component Guides**: Update usage documentation
2. **Migration Examples**: Before/after code examples
3. **Architecture Docs**: Update component architecture documentation
4. **Changelog**: Document all changes and breaking changes

## 📅 Timeline Summary

| Phase | Duration | Priority | Risk | Dependencies |
|-------|----------|----------|------|--------------|
| Phase 1 | 1-2 days | High | Low | None |
| Phase 2 | 2-3 days | High | Medium | Phase 1 |
| Phase 3 | 3-4 days | Medium | Medium | Phase 1 |
| Phase 4 | 2-3 days | Medium | Low | Phase 1 |
| Phase 5 | 4-5 days | Lower | High | Phases 1-4 |
| Phase 6 | 2-3 days | Lower | Medium | Phases 1-5 |
| Phase 7 | 2-3 days | Critical | Low | All phases |

**Total Estimated Duration:** 16-23 days  
**Recommended Approach:** Execute phases 1-4 in parallel where possible, then phases 5-7 sequentially.
