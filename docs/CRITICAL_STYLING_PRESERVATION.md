# Critical Styling Preservation Guide

This document outlines the exact styling that MUST be preserved during the dark theme color system update.

## Golden Gradient Primary Buttons

### Exact Styling Requirements
- **Gradient**: `linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%)`
- **Multi-layered Box Shadows**:
  - Inner highlight: `4px 4px 8px 0px rgba(255, 255, 255, 0.45) inset`
  - Outer shadow: `0px 4px 12px 0px rgba(0, 0, 0, 0.25)`
  - Inner dark shadow: `-4px -4px 12px 0px #604400 inset`
- **Hover Shadows**:
  - Inner highlight: `4px 4px 8px 0px rgba(255, 255, 255, 0.55) inset`
  - Outer shadow: `0px 6px 16px 0px rgba(0, 0, 0, 0.35)`
  - Inner dark shadow: `-4px -4px 12px 0px #604400 inset`
- **Focus Ring**: `focus:ring-[#E1B649]/50`
- **Font**: Rubik font family
- **Text Color**: White (#FFFFFF)
- **Hover Effect**: `hover:-translate-y-0.5`

### Tailwind Classes to Preserve
```css
.bg-golden-button {
  background: linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%);
}

.shadow-golden-button {
  box-shadow: 4px 4px 8px 0px rgba(255, 255, 255, 0.45) inset,
              0px 4px 12px 0px rgba(0, 0, 0, 0.25),
              -4px -4px 12px 0px #604400 inset;
}

.shadow-golden-button-hover {
  box-shadow: 4px 4px 8px 0px rgba(255, 255, 255, 0.55) inset,
              0px 6px 16px 0px rgba(0, 0, 0, 0.35),
              -4px -4px 12px 0px #604400 inset;
}
```

## Table Status Badge Styling

### Status Colors (CSS Variables)
```css
:root {
  /* Success/Win/Active states */
  --status-success-bg: rgba(65, 136, 118, 0.2); /* #41887633 */
  --status-success-text: #21CE9E;

  /* Failed/Loss/Inactive states */
  --status-failed-bg: #5B2424;
  --status-failed-text: #FB3D32;

  /* Pending/Processing states */
  --action-type-bg: rgba(255, 165, 0, 0.2); /* #FFA50033 */
  --action-type-text: #FFA500;
}
```

### Table Header Styling
- **Font**: Rubik, 600 weight (font-semibold), 16px size
- **Color**: White (text-white)
- **Line height**: 100% (leading-none)
- **Border Radius**: 8px on thead elements

### Table Body Styling
- **Text Color**: #999999 (text-gray-400)

## Pagination Styling

### Current Pagination Classes
```scss
.ti-pagination {
  @apply flex items-center;

  li .page-link {
    @apply border-s border-t border-b border-defaultborder dark:border-defaultborder/10
           text-defaulttextcolor leading-[1.5] dark:text-defaulttextcolor/80
           hover:text-primary py-[0.375rem] px-[0.75rem] inline-flex items-center
           text-[0.8125rem] font-normal gap-2;

    &.active {
      @apply bg-primary text-white border-primary;
    }
  }
}
```

## Component Inventory for Color Updates

### Layout Components
- `app/(components)/layout.tsx` - Main layout with CSS custom properties
- `shared/layouts-components/header/header.tsx` - Main header component
- `shared/layouts-components/header/components/HeaderLogoAndToggle.tsx`
- `shared/layouts-components/header/components/HeaderThemeToggle.tsx`
- `shared/layouts-components/navigation/HorizontalNavigation.tsx`

### Table Components
- `shared/UI/tables/GlobalDataTable.tsx` - Main table component
- `shared/UI/components/SpkTable.tsx` - Reusable table component
- `shared/components/tables/BetReportTableColumns.tsx` - Table column definitions

### Filter Components
- `shared/UI/filters/GlobalFilterSection.tsx`
- Components using filter backgrounds and borders

### Modal Components
- Components in `shared/UI/modals/`
- Authentication wrapper: `shared/UI/components/wrappers/AuthenticationBackgroundWrapper.tsx`

### Button Components (PRESERVE EXACTLY)
- `shared/UI/components/buttons/PrimaryButton.tsx`
- `shared/UI/components/SpkPrimaryButton.tsx`

### Navigation Components
- `shared/UI/components/SpkNav.tsx` - Navigation with color variants

### Alert/Badge Components
- `shared/UI/components/SpkAlert.tsx`
- `shared/components/ui-elements/alerts/BorderedAlert.tsx`
- `shared/UI/components/StatusBadge.tsx`

### Utility Components
- `shared/components/utilities/borders/BorderContainer.tsx`
- `shared/@spk-reusable-components/uielements/cards/spkbgcards.tsx`

## Verification Checklist

After theme updates, verify:
- [ ] Primary buttons maintain exact golden gradient and shadows
- [ ] Table status badges show correct colors and backgrounds
- [ ] Table headers have 8px border-radius and white text
- [ ] Table body text is #999999 color
- [ ] Pagination maintains current styling and functionality
- [ ] Page headers preserve existing layout and styling
- [ ] All hover and focus states work correctly
- [ ] No visual regressions in button or table components

## Notes

- These styles are critical to the user experience and brand identity
- Any changes to these components should be minimal and preserve visual appearance
- Test all interactive states (hover, focus, active, disabled) after updates
- Ensure responsive design is maintained across all screen sizes
