# Component Color Usage Inventory

This document provides a comprehensive inventory of all components that use background, border, or text colors and need to be updated for the new dark theme.

## Current Color System Issues

### Conflicting Definitions in Tailwind Config
- `bg-background` defined as both `#1D1D1D` and `#0F0F0F` in different sections
- `bg-section` defined as `#0F0F0F` but needs to be `#272729`
- Missing new semantic classes: `bg-filter`, `bg-table-section`, `bg-table-total`, `bg-table-head`

### CSS Variables Inconsistencies
- Many CSS variables set to `255 255 255` (white) in dark theme
- Need alignment with new semantic color palette

## Components Requiring Updates

### 1. Layout Components

#### `app/(components)/layout.tsx`
- **Current**: Uses CSS custom properties for body background
- **Update**: Ensure compatibility with new semantic classes
- **Colors Used**: `--body-bg`, `--dark-bg`, `--light`

#### `shared/layouts-components/header/header.tsx`
- **Current**: Uses app-header class
- **Update**: Apply `bg-nav` background
- **Colors Used**: Header background styling

### 2. Navigation Components

#### `shared/layouts-components/navigation/HorizontalNavigation.tsx`
- **Current**: Navigation styling
- **Update**: Apply `bg-nav` (#1D1D1D) background
- **Colors Used**: Navigation background and text colors

#### `shared/UI/components/SpkNav.tsx`
- **Current**: Multiple color variants (underline, pills, segment)
- **Update**: Update border and background colors for dark theme
- **Colors Used**: 
  - `border-gray-200 dark:border-white/10`
  - `bg-gray-100 hover:bg-gray-200`
  - `dark:bg-black/20 dark:hover:bg-black/20`

### 3. Table Components

#### `shared/UI/tables/GlobalDataTable.tsx`
- **Current**: Uses `bg-elevated` for table background
- **Update**: Apply new table semantic classes
- **Colors Used**: 
  - Table container: `bg-table-section` (#1D1D1F)
  - Table background: `bg-elevated` (#272729)
  - Header: `bg-table-head` (#313452)

#### `shared/UI/components/SpkTable.tsx`
- **Current**: Table styling with borders
- **Update**: Apply new table colors and border opacity
- **Colors Used**: Table borders, header backgrounds

#### `shared/components/tables/BetReportTableColumns.tsx`
- **Current**: Hardcoded colors in settlement column
- **Update**: Replace hardcoded `#838383` and `#83838333` with semantic classes
- **Colors Used**: Status badge backgrounds and text

### 4. Filter Components

#### Filter components in `shared/UI/filters/`
- **Current**: Various background and border colors
- **Update**: Apply new filter semantic classes
- **Colors Used**:
  - Filter headings: `bg-section` (#272729)
  - Filter backgrounds: `bg-filter` (#1D1D1F)
  - Filter borders: `#333333` for headings, `#FFFFFF33` for inputs

### 5. Modal Components

#### `shared/UI/components/wrappers/AuthenticationBackgroundWrapper.tsx`
- **Current**: Hardcoded `bg-[#0F0F0F]`
- **Update**: Replace with `bg-background` semantic class
- **Colors Used**: Background color `#0F0F0F`

### 6. Alert/Badge Components

#### `shared/UI/components/SpkAlert.tsx`
- **Current**: Dynamic color classes `bg-${variant}/20`
- **Update**: Ensure compatibility with new color palette
- **Colors Used**: Variant-based backgrounds and borders

#### `shared/components/ui-elements/alerts/BorderedAlert.tsx`
- **Current**: Dynamic color classes
- **Update**: Verify color variants work with new palette
- **Colors Used**: Alert backgrounds and borders

### 7. Utility Components

#### `shared/components/utilities/borders/BorderContainer.tsx`
- **Current**: Uses `defaultborder` color
- **Update**: Ensure border colors align with new palette
- **Colors Used**: `border-defaultborder`, dynamic border colors

#### `shared/@spk-reusable-components/uielements/cards/spkbgcards.tsx`
- **Current**: Dynamic background and text colors
- **Update**: Verify color combinations work with new theme
- **Colors Used**: `box-bg-${color}`, `text-${Textclass}`

### 8. Form Components

#### Form elements using `inputborder` and `formcontrolbg`
- **Current**: CSS variable-based styling
- **Update**: Align with new input border colors (`#FFFFFF33`)
- **Colors Used**: Input borders, form backgrounds

## Hardcoded Color Values to Replace

### In Component Files
1. `bg-[#0F0F0F]` in AuthenticationBackgroundWrapper
2. `#838383` and `#83838333` in BetReportTableColumns
3. Any other hardcoded hex values in className or style attributes

### In CSS/SCSS Files
1. Background utility classes in `public/assets/scss/util/_background.scss`
2. Border utility classes in `public/assets/scss/util/_border.scss`
3. Component-specific colors in `public/assets/scss/tailwind/_components.scss`

## Text Color Updates Required

### Filter Components
- **Headings**: `#FFFFFF` (white)
- **Labels**: `#AEAEAE` (light gray)
- **Placeholders**: `#616161` (medium gray)

### Table Components
- **Headers**: `#FFFFFF` (white) - already correct
- **Body**: `#999999` (text-gray-400) - already correct

## Border Color Updates Required

### Filter Components
- **Heading borders**: `#333333`
- **Input borders**: `#FFFFFF33` (33% opacity white)

### Table Components
- **Row borders**: `#C4C4C41A` (10% opacity white)

## Priority Order for Updates

1. **High Priority**: Tailwind config and CSS variables (foundation)
2. **High Priority**: Layout and header components (visible on all pages)
3. **Medium Priority**: Table components (frequently used)
4. **Medium Priority**: Filter components (user interaction)
5. **Low Priority**: Modal and utility components (less frequent)
6. **Low Priority**: Alert and badge components (status indicators)

## Testing Requirements

After each component update:
1. Verify visual appearance matches design requirements
2. Test responsive behavior across screen sizes
3. Verify hover and focus states work correctly
4. Ensure no TypeScript errors
5. Test component functionality remains intact
