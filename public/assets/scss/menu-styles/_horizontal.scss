/* Start:: horizontal */
[data-nav-layout="horizontal"] {
    .sidemenu-layout-styles {
        @apply hidden;
    }
    @media (min-width: 992px) {
        &[class="light"],&[class="dark"] {
            .app-sidebar .side-menu__item{
                &.active,&:hover {
                    @apply bg-transparent;
                }
            }
        }
        .app-sidebar {
            @apply shadow-defaultshadow;
        }
        .app-sidebar .side-menu__icon {
            @apply text-[0.9rem] w-[0.9rem] h-[0.9rem];
        }
        .main-menu {
            @apply flex transition-all duration-[0.5s] ease-[ease] flex-nowrap #{!important};
            &-container {
                @apply inline-flex;
            }
        }
        .app-sidebar .slide-menu.child1 .side-menu__item, .app-sidebar .slide-menu.child2 .side-menu__item {
            @apply px-4 py-[0.45rem];
        }
        .side-menu__label .badge {
            @apply hidden;
        }
        .mega-menu {
            @apply columns-3;
        }
        .app-header {
            @apply z-[104] shadow-none ps-0 #{!important};
            .main-header-container .header-element.header-search {
                @apply ms-5;
            }
        }   
        .app-content {
            @apply min-h-[calc(100vh_-_10.5rem)] ms-0 mt-[7rem];
            > .container-fluid {
                @apply w-[94%] mx-auto px-[7px];
            }
        }
        .app-content {
            @apply ms-0 #{!important};
        }
        .app-sidebar {
            .main-sidebar,
            .simplebar-mask {
                @apply overflow-visible;
            }
            .main-menu > .slide {
                @apply mx-[0.1875rem] my-0;
            }
            .main-sidebar {
                @apply shadow-none;
            }
            .slide-menu{
                &.child1,&.child2,&.child3 {
                    .side-menu__item:before {
                        @apply hidden;
                    }
                }
            } 
        }
        .simplebar-offset {
            position: inherit !important;
        }
        .simplebar-wrapper {
            @apply w-full;
        }
        .main-sidebar .simplebar-vertical {
            @apply invisible #{!important};
        }
        .main-sidebar,
        .main-header-container {
            @apply w-[94%] mx-auto my-0;
        }
        .horizontal-logo {
            @apply block px-0 py-2;
            @media (min-width: 992px) {
                .header-logo {
                    img {
                        @apply h-[1.7rem] leading-[1.7rem];
                    }
                    .desktop-logo {
                        @apply block;
                    }
                    .desktop-dark,
                    .desktop-white,
                    .toggle-logo,
                    .toggle-white,
                    .toggle-dark {
                        @apply hidden;
                    }
                }
            }
        }
        .main-header-container .sidemenu-toggle {
            @apply hidden;
        }
        .app-sidebar {
            @apply w-full h-auto border-b-menubordercolor border-b border-solid top-[4.25rem] dark:border-defaultborder/10 #{!important};
            .slide-menu.child1,
            .slide-menu.child2,
            .slide-menu.child3 {
                li {
                    @apply ps-2;
                }
                .side-menu__item:before {
                    @apply top-4;
                }
            }
            .simplebar-content {
                @apply overflow-hidden p-0 #{!important};
            }
            .simplebar-content-wrapper {
                @apply overflow-visible h-auto;
            }
            .main-sidebar {
                @apply p-0;
            }
            .slide.has-sub .slide-menu {
                &.child1 {
                    @apply px-0 py-[0.1875rem] start-6;
                }
                &.child2,
                &.child3 {
                    @apply px-0 py-[0.1875rem] end-full;
                }
                &.active {
                    @apply inset-x-auto #{!important};
                }
            }
            .slide-menu.child1 {
                @apply absolute #{!important};
            }
            .side-menu__item {
                @apply w-full flex p-[0.92rem] rounded-none;
            }
            .side-menu__angle {
                @apply block end-1;
            }
            .side-menu__icon {
                @apply me-1 mb-0;
            }
            .slide.has-sub .slide-menu.child1 {
                @apply bg-white dark:bg-bodybg min-w-[12rem] top-full #{!important};
            }
            .slide {
                @apply p-0;
            }
            .slide-menu {
                &.child1 {
                    @apply rounded-[0_0_0.25rem_0.25rem];
                    .slide.has-sub,.slide {
                        @apply w-full flex px-[0.1875rem] py-0;
                    }
                }
                &.child2,
                &.child3 {
                    @apply rounded-md;
                }
            }
            .slide,.slide.has-sub {
                position: static;
                .slide-menu {
                    &.child1,&.child2,&.child3 {
                        @apply shadow-[0_0_0.375rem_black];
                    }
                }
            }
            .main-menu {
                @apply mb-0;
            }
        }
        .main-sidebar-header {
            @apply hidden #{!important}; 
        }
        .main-sidebar {
            @apply mt-0 pb-12;
        }
        .slide__category {
            @apply hidden;
        }
        /* horizontal arrows */
        .main-menu-container .slide-left {
            @apply start-[1.438rem];
        }
        .main-menu-container .slide-left,
        .main-menu-container .slide-right {
            @apply absolute text-customwhite flex items-center justify-center z-[1] cursor-pointer border border-defaultborder p-1.5 rounded-[3.125rem] border-solid top-[0.563rem];
        }

        .main-menu-container .slide-left,
        .main-menu-container .slide-right {
            @apply absolute text-customwhite flex items-center justify-center z-[1] cursor-pointer border border-defaultborder bg-white dark:bg-bodybg p-1.5 rounded-[3.125rem] border-solid top-[0.313rem];
        }

        .main-menu-container {
            .slide-right {
                @apply end-[-2%];
            }
            .slide-left {
                @apply start-[-2%];
            }
        }
        &[page-style="classic"] {
            .app-sidebar {
                @apply border-b-defaultborder border-b border-solid;
            }
        }
        &[dir="rtl"] {
            .main-menu-container {
                .slide-right,.slide-left {
                    @apply rotate-180;
                }
            }
        }
        &[data-menu-styles="transparent"][class="dark"][data-icon-overlay="open"] .app-sidebar:hover {
            @apply backdrop-blur-none;
        }
        &[data-menu-styles="light"] .main-menu-container {
            .slide-right, .slide-left {
                --custom-white: 255 255 255;
                --default-border: 243 243 243;
            }
        }
        &[data-page-style="modern"] {
        .app-sidebar {
            @apply border-b-headerbordercolor border-b border-solid;
            }
        }
        &[data-menu-styles="transparent"][class="dark"] {
            .app-sidebar {
                @apply backdrop-blur-[30px];
            }
        }
        &[data-nav-style="menu-click"],
        &[data-nav-style="menu-hover"],
        &[data-nav-style="icon-click"],
        &[data-nav-style="icon-hover"] {
          .app-sidebar {
            .slide.has-sub .slide-menu {
                &.child1 {
                    &.force-left {
                        @apply end-0 #{!important};
                    }
                    
                }
              &.child2,
              &.child3 {
                &.force-left {
                    @apply -start-full #{!important};
                }
              }
            }
          }
        }
    }
}
@media (max-width: 991.98px) {
    [data-nav-layout=horizontal] .horizontal-logo .header-logo img {
        @apply h-[1.7rem] leading-[1.7rem];
    }
}
/* End:: horizontal */