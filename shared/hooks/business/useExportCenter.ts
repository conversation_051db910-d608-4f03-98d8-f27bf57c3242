// shared/hooks/business/useExportCenter.ts - Business logic hook for Export Center page

import { useState, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/shared/stores/authStore';
import { useExportCenterQuery, useExportCenterRefetch } from '@/shared/query/useExportQuery';
import {
  ExportCenterFilters,
  ExportCenterResponse,
  ExportCenterItem,
  DEFAULT_EXPORT_CENTER_FILTERS
} from '@/shared/types/export-types';

interface UseExportCenterProps {
  initialExportCenterResponse?: ExportCenterResponse | undefined;
  initialFilters?: ExportCenterFilters;
}

// Transformed item interface for display purposes
export interface TransformedExportCenterItem {
  id: string;
  type: string;
  module: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
  downloadUrl?: string; // Relative URL from API (csv_url field)
  fullDownloadUrl?: string; // Complete URL for direct download (base URL + csv_url)
  fileName?: string;
  fileSize?: string;
  errorMessage?: string;
  // Original raw data for reference
  raw: ExportCenterItem;
}

interface UseExportCenterReturn {
  // Data
  filters: ExportCenterFilters;
  exportCenterResponse: ExportCenterResponse | undefined;
  exportData: TransformedExportCenterItem[];

  // Loading states
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isFetching: boolean;

  // Pagination
  totalRecords: number;
  totalPages: number;
  currentPage: number;

  // Actions
  handleFilterChange: (newFilters: Partial<ExportCenterFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Auth
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

/**
 * Custom hook for Export Center page business logic
 * Handles data fetching, filtering, pagination, and download functionality
 */
export const useExportCenter = ({
  initialExportCenterResponse = undefined,
  initialFilters
}: UseExportCenterProps = {}): UseExportCenterReturn => {
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // State for filters
  const [filters, setFilters] = useState<ExportCenterFilters>({
    ...DEFAULT_EXPORT_CENTER_FILTERS,
    ...initialFilters
  });

  // Fetch export center data using the query hook
  const {
    data: exportCenterResponse,
    isLoading,
    isError,
    error,
    isFetching
  } = useExportCenterQuery(filters);

  // Get refetch function
  const refetchExportCenter = useExportCenterRefetch();

  // Use initial data if available, otherwise use query data
  const finalExportCenterResponse = exportCenterResponse || initialExportCenterResponse;

  // Calculate pagination values using the new response structure
  const totalRecords = finalExportCenterResponse?.record?.total || 0;
  const totalPages = finalExportCenterResponse?.record?.last_page || Math.ceil(totalRecords / filters.size) || 1;
  const currentPage = filters.page;

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<ExportCenterFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      // Reset to page 1 when filters change (except for page changes)
      page: newFilters.page !== undefined ? newFilters.page : 1
    }));
  }, []);

  // Handle page changes
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({
      ...prev,
      page
    }));
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetchExportCenter();
  }, [refetchExportCenter]);

  // Download functionality is now handled directly by anchor tags in components

  // Redirect to login if not authenticated (after hydration)
  const hasHydrated = _hasHydrated;
  if (hasHydrated && !isAuthenticated) {
    router.push('/authentication/sign-in');
  }

  // Memoize return object to prevent unnecessary re-renders
  return useMemo(() => {
    // Transform API response to match component expectations
    const transformExportData = (response: ExportCenterResponse | undefined): TransformedExportCenterItem[] => {
      // Get data from the new response structure
      const rawData: ExportCenterItem[] = response?.record?.data || [];

      return rawData.map((item: ExportCenterItem): TransformedExportCenterItem => {
        // Map numeric status to string status
        const getStatusString = (numericStatus: number, progressBar?: string): 'pending' | 'processing' | 'completed' | 'failed' => {
          // If progress_bar is "100", it's completed regardless of status number
          if (progressBar === "100") {
            return 'completed';
          }

          switch (numericStatus) {
            case 1: return 'pending';
            case 2: return 'processing';
            case 3: return 'completed';
            case 4: return 'failed';
            default: return 'pending';
          }
        };

        // Determine module based on type
        const getModuleFromType = (type: string): string => {
          switch (type) {
            case 'cashier_bet_report': return 'bet_report';
            case 'casino_transactions_db': return 'casino_transactions';
            default: return 'unknown';
          }
        };

        // Construct full download URL if csv_url exists
        const baseUrl = process.env.NEXT_PUBLIC_IMAGES_BASE_URL || 'https://assets.roylfc.com';
        const fullDownloadUrl = item.csv_url ? `${baseUrl}/${item.csv_url}` : undefined;

        return {
          id: String(item.id),
          type: item.type,
          module: getModuleFromType(item.type),
          status: getStatusString(item.status, item.progress_bar),
          createdAt: item.created_at,
          updatedAt: item.updated_at,
          downloadUrl: item.csv_url || undefined, // Relative URL from API
          fullDownloadUrl, // Complete URL for direct download
          fileName: undefined, // Not provided in API response
          fileSize: undefined, // Not provided in API response
          errorMessage: undefined, // Not provided in API response
          raw: item // Keep original data for reference
        };
      });
    };

    // Safely extract and transform export data array from response
    const exportData = transformExportData(finalExportCenterResponse);

    // Debug logging in development
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('Export Center Debug:', {
        originalResponse: finalExportCenterResponse,
        transformedData: exportData,
        dataLength: exportData.length
      });
    }

    return {
      // Data
      filters,
      exportCenterResponse: finalExportCenterResponse,
      exportData,

      // Loading states
      isLoading,
      isError,
      error,
      isFetching,

      // Pagination
      totalRecords,
      totalPages,
      currentPage,

      // Actions
      handleFilterChange,
      handlePageChange,
      handleRefresh,

      // Auth
      isAuthenticated,
      hasHydrated
    };
  }, [
    filters,
    finalExportCenterResponse,
    isLoading,
    isError,
    error,
    isFetching,
    totalRecords,
    totalPages,
    currentPage,
    handleFilterChange,
    handlePageChange,
    handleRefresh,
    isAuthenticated,
    hasHydrated
  ]);
};
