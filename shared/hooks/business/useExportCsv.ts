// shared/hooks/business/useExportCsv.ts - Business logic hook for CSV export functionality

import { useCallback } from 'react';
import { useExportRequestMutation } from '@/shared/query/useExportQuery';
import { useToast } from '@/shared/UI/components';
import {
  ExportRequestPayload,
  ExportModuleType,
  ExportDataType
} from '@/shared/types/export-types';
import {
  CashierReportFilters,
  BetReportFilters,
  FinancialReportFilters
} from '@/shared/types/report-types';
import { transformActionTypeForApi } from '@/shared/config/transactionTypes';

// Type for all possible filter types - Only supported report types
type ReportFilters = CashierReportFilters | BetReportFilters | FinancialReportFilters;

interface UseExportCsvProps {
  module: ExportModuleType;
  type: ExportDataType;
}

interface UseExportCsvReturn {
  exportCsv: (filters: ReportFilters) => Promise<void>;
  isExporting: boolean;
}

/**
 * Custom hook for CSV export functionality
 * Handles the business logic for exporting data from different report pages
 */
export const useExportCsv = ({ module, type }: UseExportCsvProps): UseExportCsvReturn => {
  const { showSuccess, showError } = useToast();
  const exportMutation = useExportRequestMutation();

  // Format filters for casino transactions (cashier report)
  const formatCasinoTransactionFilters = useCallback((filters: CashierReportFilters) => {
    return {
      size: filters.size || 25,
      page: filters.page || 1,
      search: filters.search || '',
      transactionId: filters.transactionId || '',
      debitTransactionId: filters.debitTransactionId || '',
      roundId: filters.roundId || '',
      tenantId: filters.tenantId || '',
      order: filters.order || 'desc',
      sortBy: filters.sortBy || 'created_at',
      actionType: transformActionTypeForApi(filters.actionType) || '',
      timePeriod: filters.timePeriod || '',
      userId: filters.userId || '',
      agentId: filters.agentId || '',
      timeZone: filters.timeZone || 'UTC +05:30',
      timeZoneName: filters.timeZoneName || 'Asia/Colombo',
      dateTime: filters.dateTime || ''
    };
  }, []);

  // Format filters for financial report
  const formatFinancialReportFilters = useCallback((filters: FinancialReportFilters) => {
    return {
      size: filters.size || 25,
      page: filters.page || 1,
      search: filters.search || '',
      amount: filters.amount || '',
      transactionId: filters.transactionId || '',
      debitTransactionId: filters.debitTransactionId || '',
      roundId: filters.roundId || '',
      utrNumber: filters.utrNumber || '',

      tenantId: filters.tenantId || '',
      order: filters.order || 'desc',
      sortBy: filters.sortBy || 'created_at',
      currencyId: filters.currencyId || '',
      actionType: transformActionTypeForApi(filters.actionType) || '',
      actionCategory: 'financial', // Always set for financial reports
      timePeriod: filters.timePeriod || '',
      playerId: filters.playerId || '',
      timeZone: filters.timeZone || 'UTC +05:30',
      timeZoneName: filters.timeZoneName || 'Asia/Colombo',
      gameProvider: filters.gameProvider || '',
      gameType: filters.gameType || '',

      dateTime: filters.dateTime || ''
    };
  }, []);

  // Format filters for bet report
  const formatBetReportFilters = useCallback((filters: BetReportFilters) => {
    return {
      page: filters.page?.toString() || '1',
      limit: filters.limit?.toString() || '10',
      startDate: filters.startDate || '',
      endDate: filters.endDate || '',
      status: filters.status || '',
      payoutStatus: filters.payoutStatus || '',
      transactionId: filters.transactionId || '',
      playerId: filters.playerId || '',
      marketId: filters.marketId || ''
    };
  }, []);

  // Main export function
  const exportCsv = useCallback(async (filters: ReportFilters) => {
    try {
      let formattedFilters: any;

      // Format filters based on module type - Only supported report types
      switch (module) {
        case 'casino_transactions':
          formattedFilters = formatCasinoTransactionFilters(filters as CashierReportFilters);
          break;
        case 'financial_report':
          formattedFilters = formatFinancialReportFilters(filters as FinancialReportFilters);
          break;
        case 'bet_report':
          formattedFilters = formatBetReportFilters(filters as BetReportFilters);
          break;
        default:
          throw new Error(`Unsupported module type: ${module}`);
      }

      // Create export request payload
      const payload: ExportRequestPayload = {
        payload: JSON.stringify(formattedFilters),
        module,
        type
      };

      // Submit export request
      await exportMutation.mutateAsync(payload);

      // Show success notification
      showSuccess(
        'Export Request Submitted',
        'Your CSV export request has been submitted successfully. You can check the progress in the Export Center.'
      );

    } catch (error) {
      // Log error for debugging in development
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.error('Export failed:', error);
      }
      showError(
        'Export Failed',
        error instanceof Error ? error.message : 'Failed to submit export request. Please try again.'
      );
    }
  }, [
    module,
    type,
    exportMutation,
    showSuccess,
    showError,
    formatCasinoTransactionFilters,
    formatFinancialReportFilters,
    formatBetReportFilters
  ]);

  return {
    exportCsv,
    isExporting: exportMutation.isPending
  };
};
