'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSidebarToggle } from '@/shared/hooks'; // Use hook from parent Header context

interface HeaderLogoAndToggleProps {
    basePath: string;
}

export const HeaderLogoAndToggle: React.FC<HeaderLogoAndToggleProps> = ({ basePath }) => {
    const { toggleSidebar } = useSidebarToggle(); // Consume the hook here

    return (
        <div className="header-content-left">
            <div className="header-element">
                <div className="horizontal-logo">
                    <Link href="/dashboard/sales" className="header-logo h-[50px] flex items-center ">
                        <Image
                            src={`${process.env.NODE_ENV === "production" ? basePath : ""}/assets/images/header-logo.png`}
                            alt="Golden Island Logo"
                            className="desktop-logo w-auto h-full"
                            width={84}
                            height={43}
                            style={{height:'100%'}}
                            priority
                        />
                        <Image
                            src={`${process.env.NODE_ENV === "production" ? basePath : ""}/assets/images/header-logo.png`}
                            alt="Golden Island Logo"
                            className="toggle-dark w-auto h-full"
                            width={84}
                            height={43}
                            style={{height:'100%'}}
                            priority
                        />
                        <Image
                            src={`${process.env.NODE_ENV === "production" ? basePath : ""}/assets/images/header-logo.png`}
                            alt="Golden Island Logo"
                            className="desktop-dark w-auto h-full"
                            width={84}
                            height={43}
                            style={{height:'100%'}}
                            priority
                        />
                        <Image
                            src={`${process.env.NODE_ENV === "production" ? basePath : ""}/assets/images/header-logo.png`}
                            alt="Golden Island Logo"
                            className="toggle-logo w-auto h-full"
                            width={84}
                            height={43}
                            style={{height:'100%'}}
                            priority
                        />
                        <Image
                            src={`${process.env.NODE_ENV === "production" ? basePath : ''}/assets/images/header-logo.png`}
                            alt="Golden Island Logo"
                            className="toggle-white w-auto h-full"
                            width={84}
                            height={43}
                            style={{height:'100%'}}
                            priority
                        />
                        <Image
                            src={`${process.env.NODE_ENV === 'production' ? basePath : ''}/assets/images/header-logo.png`}
                            alt="Golden Island Logo"
                            className="desktop-white w-auto h-full"
                            width={84}
                            height={43}
                            style={{height:'100%'}}
                            priority
                        />
                    </Link>
                </div>
            </div>
            <div className="header-element mx-lg-0">
                <Link aria-label="Hide Sidebar" onClick={toggleSidebar} className="sidemenu-toggle header-link animated-arrow hor-toggle horizontal-navtoggle" data-bs-toggle="sidebar" scroll={false} href="#!">
                    <span></span>
                </Link>
            </div>
        </div>
    );
};
