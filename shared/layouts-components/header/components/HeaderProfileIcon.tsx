// shared/layouts-components/header/components/HeaderProfileIcon.tsx
'use client';

import React from 'react';
import Image from 'next/image';
import { useLogoutHandler } from '@/shared/hooks';

interface HeaderProfileIconProps {
    basePath: string;
}

/**
 * Header Profile Icon Component
 *
 * Displays profile icon with "Logout" text label as per design specifications:
 * - Text: Rubik font, 400 weight, 14px size, 100% line height, #E1B649 color
 * Maintains logout functionality with visual presentation
 */
export const HeaderProfileIcon: React.FC<HeaderProfileIconProps> = ({ basePath }) => {
    const { handleLogout, isLoggingOut } = useLogoutHandler();

    return (
        <li className="header-element profile-icon">
            <button
                type="button"
                onClick={handleLogout}
                disabled={isLoggingOut}
                className="flex items-center gap-2 transition-all duration-200 hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-white/20 rounded cursor-pointer"
                title={isLoggingOut ? 'Logging out...' : 'Logout'}
                aria-label={isLoggingOut ? 'Logging out...' : 'Logout'}
            >
                {isLoggingOut ? (
                    <div className="animate-spin rounded-full border-2 border-white/20 border-t-white w-5 h-5" />
                ) : (
                    <Image
                        src={`${process.env.NODE_ENV === "production" ? basePath : ""}/assets/images/profile.png`}
                        alt="Profile"
                        width={32}
                        height={32}
                        className="rounded-full"
                        priority
                    />
                )}
                <span className="font-rubik font-normal text-sm leading-none text-golden">
                    {isLoggingOut ? 'Logging out...' : 'Logout'}
                </span>
            </button>
        </li>
    );
};
