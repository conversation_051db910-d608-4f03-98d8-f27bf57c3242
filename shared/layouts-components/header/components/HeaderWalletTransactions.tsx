// shared/layouts-components/header/components/HeaderWalletTransactions.tsx
'use client';

import React, { useState } from 'react';
import { WalletTransactionModal } from '@/shared/UI/components';
import { useAdminWalletQuery, getPrimaryAdminWallet } from '@/shared/query/useAdminWalletQuery';

const HeaderWalletTransactions: React.FC = () => {
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);
  const [walletTransactionType] = useState<"deposit" | "withdraw">("deposit");

  // Fetch admin wallet data
  const { data: adminWalletData, isLoading: isWalletLoading, error: _walletError } = useAdminWalletQuery();
  const primaryWallet = getPrimaryAdminWallet(adminWalletData as any);

  const handleWalletClick = () => {
    setIsWalletModalOpen(true);
  };

  const handleWalletTransactionSuccess = (_transactionId: number) => {
    // The mutation hook will automatically invalidate queries and refresh data
  };

  // Format wallet amount for display
  const formatWalletAmount = (amount: string, currency: string) => {
    const numAmount = parseFloat(amount);
    if (numAmount >= 1000000) {
      return `${(numAmount / 1000000).toFixed(1)}M ${currency}`;
    } else if (numAmount >= 1000) {
      return `${(numAmount / 1000).toFixed(1)}K ${currency}`;
    }
    return `${numAmount.toLocaleString()} ${currency}`;
  };

  return (
    <>
      {/* Wallet Section with Design Specifications */}
      <li className="header-element">
        <div className="flex items-center justify-center gap-3 rounded-[4px] bg-surface px-2 py-1.5">
          {/* Wallet Amount Display */}
          <span className="text-white font-rubik font-medium text-sm leading-none">
            {isWalletLoading ? '...' : primaryWallet ? `$${formatWalletAmount(primaryWallet.amount, primaryWallet.currency_name)}` : '$0.00'}
          </span>

          {/* Wallet Button */}
          <button
            type="button"
            className="flex items-center rounded-[4px] bg-golden-button text-white font-rubik font-medium text-xs leading-none gap-1 w-[65px] h-[26px] px-1.5 py-1 border-none cursor-pointer transition-all duration-200 hover:opacity-90 shadow-golden-button hover:shadow-golden-button-hover"
            onClick={handleWalletClick}
            title={primaryWallet ? `Wallet Balance: ${formatWalletAmount(primaryWallet.amount, primaryWallet.currency_name)}` : "Wallet Transactions"}
          >
            <i className="ri-wallet-3-line text-xs text-white" />
            <span>Wallet</span>
          </button>
        </div>
      </li>

      {/* Wallet Transaction Modal */}
      <WalletTransactionModal
        isOpen={isWalletModalOpen}
        onClose={() => setIsWalletModalOpen(false)}
        transactionType={walletTransactionType}
        onSuccess={handleWalletTransactionSuccess}
        showUserSelection={true}
        context="global"
        balance={primaryWallet ? parseFloat(primaryWallet.amount) : 10000.00}
      />
    </>
  );
};

export default HeaderWalletTransactions;
