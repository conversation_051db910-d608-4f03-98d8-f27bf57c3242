// shared/layouts-components/header/components/HeaderBalanceDisplay.tsx
'use client';

import React from 'react';
import { useHeaderBalanceLogic } from '@/shared/hooks';

interface HeaderBalanceDisplayProps {
    // Add any additional props as needed
}

/**
 * Header Balance Display Component
 * 
 * Displays user balance in the format shown in the reference image ($ 5000.00)
 * Pure UI component - all logic handled by useHeaderBalanceLogic hook
 */
export const HeaderBalanceDisplay: React.FC<HeaderBalanceDisplayProps> = () => {
    const { balance: _balance, isLoading, formattedBalance } = useHeaderBalanceLogic();

    return (
        <li className="header-element balance-display hidden md:block">
            <div className="flex items-center text-white text-sm font-medium">
                <i className="ri-wallet-3-line text-base mr-2" aria-hidden="true" />
                <span className="text-white font-semibold">
                    {isLoading ? 'Loading...' : formattedBalance}
                </span>
            </div>
        </li>
    );
};
