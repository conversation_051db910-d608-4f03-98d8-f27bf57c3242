// shared/UI/skeletons/index.ts - Export all skeleton components

export { default as TableSkeleton } from './TableSkeleton';
export { default as CardSkeleton } from './CardSkeleton';
export { default as PageSkeleton } from './PageSkeleton';
export { default as FormSkeleton } from './FormSkeleton';
export { default as AuthenticationSkeleton } from './AuthenticationSkeleton';
export { default as UserStatisticsSkeleton } from './UserStatisticsSkeleton';
export { default as ModalSkeleton } from './ModalSkeleton';
export { default as NavigationSkeleton } from './NavigationSkeleton';
export { default as SportsbookSkeleton } from './SportsbookSkeleton';

export type { TableSkeletonProps } from './TableSkeleton';
export type { CardSkeletonProps } from './CardSkeleton';
export type { PageSkeletonProps } from './PageSkeleton';
export type { FormSkeletonProps } from './FormSkeleton';
export type { AuthenticationSkeletonProps } from './AuthenticationSkeleton';
export type { UserStatisticsSkeletonProps } from './UserStatisticsSkeleton';
export type { ModalSkeletonProps } from './ModalSkeleton';
export type { NavigationSkeletonProps } from './NavigationSkeleton';
export type { SportsbookSkeletonProps } from './SportsbookSkeleton';
