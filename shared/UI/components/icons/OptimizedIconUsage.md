# Optimized Icon System Usage Guide

## Overview

The optimized Icon system now supports both legacy external SVG URLs and high-performance local SVG loading with caching, type safety, and multiple fallback strategies.

## Key Improvements

### 1. **Performance Optimizations**
- **Caching**: SVG content is cached after first load
- **Multiple Path Resolution**: Automatically tries different asset locations
- **Memoization**: Components are memoized to prevent unnecessary re-renders
- **Preloading**: Ability to preload commonly used icons

### 2. **Type Safety**
- **SVGIconName**: Typed icon names with autocomplete support
- **Discriminated Unions**: Type-safe props based on icon type
- **Fallback Support**: Type-safe fallback components

### 3. **Developer Experience**
- **Automatic Path Resolution**: No need to specify full paths
- **Error Handling**: Graceful degradation with meaningful error messages
- **Debug Utilities**: Cache inspection and clearing utilities

## Usage Examples

### Local SVG Icons (Recommended)

```tsx
import { Icon } from '@/shared/UI/components/icons';

// Basic usage
<Icon
  type="SVG_LOCAL"
  name="user-add"
  size={24}
  className="text-primary"
/>

// With fallback
<Icon
  type="SVG_LOCAL"
  name="custom-icon"
  size={32}
  fallback={<span>👤</span>}
  aria-label="User icon"
/>

// Crypto currency icons (automatically resolves path)
<Icon
  type="SVG_LOCAL"
  name="bitcoin"
  size={20}
  className="text-orange-500"
/>
```

### Direct SVGLoader Usage

```tsx
import { SVGLoader } from '@/shared/UI/components/icons';

<SVGLoader
  name="ethereum"
  size={24}
  className="text-blue-500"
  fallback={<div className="w-6 h-6 bg-gray-300 rounded" />}
/>
```

### Legacy External SVG Support

```tsx
// Still supported for external URLs
<Icon
  type="SVG"
  url="https://example.com/icon.svg"
  size={24}
  sanitize={true}
/>
```

## Performance Features

### Preloading Icons

```tsx
import { preloadSVGs } from '@/shared/UI/components/icons';

// Preload commonly used icons on app startup
useEffect(() => {
  preloadSVGs(['user-add', 'settings', 'dashboard', 'bitcoin', 'ethereum']);
}, []);
```

### Cache Management

```tsx
import { getSVGCacheInfo, clearSVGCache } from '@/shared/UI/components/icons';

// Debug cache status
console.log(getSVGCacheInfo());
// Output: { size: 5, keys: ['user-add', 'settings', ...] }

// Clear cache (useful in development)
clearSVGCache();
```

## Asset Organization

The system automatically searches for SVGs in these locations (in order):

1. `/assets/icons/${name}.svg` - Primary icon directory
2. `/assets/images/${name}.svg` - General images directory  
3. `/assets/images/crypto-currencies/round-icons/${name}.svg` - Crypto icons
4. `/assets/images/crypto-currencies/round-icons/${Name}.svg` - Capitalized crypto icons
5. `/${name}.svg` - Root level
6. `/assets/icons/ui/${name}.svg` - UI-specific icons
7. `/assets/icons/crypto/${name}.svg` - Crypto-specific icons

### Recommended Directory Structure

```
public/
├── assets/
│   ├── icons/
│   │   ├── ui/
│   │   │   ├── user-add.svg
│   │   │   ├── settings.svg
│   │   │   └── dashboard.svg
│   │   ├── crypto/
│   │   │   ├── bitcoin.svg
│   │   │   ├── ethereum.svg
│   │   │   └── litecoin.svg
│   │   └── social/
│   │       ├── facebook.svg
│   │       └── twitter.svg
│   └── images/
│       └── crypto-currencies/
│           └── round-icons/ (existing)
└── globe.svg (existing)
```

## Migration Guide

### From External URLs to Local

**Before:**
```tsx
<Icon
  type="SVG"
  url="/assets/images/crypto-currencies/round-icons/Bitcoin.svg"
  size={24}
/>
```

**After:**
```tsx
<Icon
  type="SVG_LOCAL"
  name="bitcoin"
  size={24}
/>
```

### Adding New Icon Types

```tsx
// In SVGLoader.tsx, extend the SVGIconName type:
export type SVGIconName = 
  | 'user-add'
  | 'settings'
  | 'dashboard'
  | 'wallet'
  | 'bitcoin'
  | 'ethereum'
  | 'litecoin'
  | 'new-icon-name' // Add new icons here
  | string;
```

## Performance Comparison

| Feature | External SVG | Local SVG (Optimized) |
|---------|-------------|----------------------|
| Network Requests | Every render | First load only |
| Caching | Browser cache | In-memory + browser |
| Loading States | Always shown | Cached = instant |
| Bundle Size | Smaller | Slightly larger |
| Reliability | Network dependent | Always available |
| Type Safety | URL strings | Typed icon names |

## Best Practices

1. **Use SVG_LOCAL for project icons**: Better performance and reliability
2. **Organize icons by category**: Use subdirectories for better organization
3. **Preload critical icons**: Preload icons used on initial page load
4. **Provide fallbacks**: Always provide fallbacks for custom icons
5. **Use consistent naming**: Use kebab-case for icon names
6. **Optimize SVG files**: Ensure SVGs are optimized before adding to project

## Troubleshooting

### Icon Not Found
- Check if the SVG file exists in any of the search paths
- Verify the icon name matches the filename (case-sensitive)
- Use browser dev tools to see which paths are being tried

### Performance Issues
- Use `getSVGCacheInfo()` to check cache status
- Consider preloading frequently used icons
- Ensure SVG files are optimized and not too large

### Type Errors
- Ensure icon names are added to the `SVGIconName` type
- Use the correct icon type (`SVG_LOCAL` vs `SVG`)
- Check that all required props are provided
