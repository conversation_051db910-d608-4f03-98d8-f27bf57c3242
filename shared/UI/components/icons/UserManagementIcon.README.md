# UserManagementIcon Component

## Overview

The `UserManagementIcon` component is a specialized icon component designed specifically for user management operations. It provides themed SVG icons for edit, add, and deactivate actions with consistent styling and color schemes.

## Features

- **Three Action Types**: Edit (purple), Add (orange), Deactivate (red)
- **Consistent Theming**: Predefined color schemes for each action type
- **Customizable**: Support for custom themes and sizing
- **Accessibility**: Full ARIA support and screen reader compatibility
- **TypeScript**: Complete type safety with discriminated unions
- **Performance**: Inline SVG rendering for optimal performance

## Usage

### Basic Usage

```tsx
import { UserManagementIcon } from '@/shared/UI/components/icons';

// Edit user icon
<UserManagementIcon action="edit" />

// Add user icon
<UserManagementIcon action="add" />

// Deactivate user icon
<UserManagementIcon action="deactivate" />
```

### With Custom Sizing

```tsx
<UserManagementIcon 
  action="edit" 
  size={32} 
  className="mr-2" 
/>
```

### With Accessibility

```tsx
<UserManagementIcon 
  action="deactivate" 
  ariaLabel="Deactivate user account"
  title="Click to deactivate this user"
/>
```

### With Custom Theme

```tsx
<UserManagementIcon 
  action="edit" 
  theme={{
    backgroundColor: '#FF0000',
    backgroundOpacity: '0.3',
    iconColor: '#FF0000'
  }}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `action` | `'edit' \| 'add' \| 'deactivate'` | Required | The type of user management action |
| `className` | `string` | `''` | Additional CSS classes |
| `size` | `number` | `40` | Icon size in pixels (width and height) |
| `theme` | `Partial<UserManagementIconTheme>` | - | Custom theme override |
| `ariaLabel` | `string` | - | Accessibility label for screen readers |
| `ariaHidden` | `boolean` | `false` | Whether to hide from screen readers |
| `title` | `string` | - | Tooltip title |

## Theme Configuration

Each action type has a predefined theme:

### Edit Theme (Purple)
- Background: `#9E5CF7` with 20% opacity
- Icon: `#9E5CF7`

### Add Theme (Orange)
- Background: `#FF8E6F` with 20% opacity
- Icon: `#FF8E6F`

### Deactivate Theme (Red)
- Background: `#FB4242` with 20% opacity
- Icon: `#FB4242`

## Integration with UserManagementModal

The component is designed to work seamlessly with the `UserManagementModal`:

```tsx
<UserManagementIcon 
  action={mode === 'create' ? 'add' : mode === 'edit' ? 'edit' : 'deactivate'}
  size={40}
  ariaHidden={true}
/>
```

## Design System Compliance

- Follows the established dark theme color palette
- Maintains consistent sizing and spacing
- Uses semantic color meanings (purple for edit, orange for add, red for destructive actions)
- Integrates with the existing icon system architecture

## Performance

- Uses inline SVG for optimal rendering performance
- No external dependencies or network requests
- Minimal bundle size impact
- Efficient re-rendering with React.memo compatibility

## Accessibility

- Supports ARIA labels and descriptions
- Can be hidden from screen readers when decorative
- Provides meaningful tooltips
- Follows WCAG guidelines for color contrast

## Migration from Inline SVGs

**Before (Inline SVG):**
```tsx
<svg width="40" height="40" viewBox="0 0 40 40" fill="none">
  <rect width="40" height="40" rx="4.57143" fill="#9E5CF7" fill-opacity="0.2"/>
  <path d="..." fill="#9E5CF7"/>
</svg>
```

**After (UserManagementIcon):**
```tsx
<UserManagementIcon action="edit" size={40} />
```

## Benefits

1. **DRY Principle**: Eliminates code duplication across components
2. **Maintainability**: Centralized icon definitions for easy updates
3. **Consistency**: Ensures uniform styling across the application
4. **Type Safety**: Prevents incorrect usage with TypeScript
5. **Reusability**: Can be used in any component requiring user management icons
6. **Flexibility**: Supports customization while maintaining defaults

## Related Components

- `Icon` - General-purpose icon component
- `UserManagementModal` - Primary consumer of this component
- `GlobalPageHeader` - May use for action buttons
- `SpkTable` - May use for action columns
