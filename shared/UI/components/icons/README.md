# Icon System Documentation

## Overview

The Icon System provides a flexible, type-safe way to render icons across the application. It supports multiple icon types including font icons, SVG icons, image icons, and React component icons.

## Features

- **Multiple Icon Types**: Support for font icons, SVG, images, and React components
- **TypeScript Safety**: Full TypeScript support with discriminated unions
- **SVG Security**: Built-in SVG sanitization for security
- **Consistent API**: Unified interface across all icon types
- **Accessibility**: Full ARIA support and accessibility features
- **Performance**: Optimized rendering and caching
- **Error Handling**: Graceful error handling and loading states

## Icon Types

### 1. Font Icons (FONT_ICON)

Renders font-based icons from various icon libraries:

```tsx
<Icon
  type="FONT_ICON"
  iconClass="ri-user-add-line"
  library="remix"
  size={24}
  className="text-white"
/>
```

**Supported Libraries:**
- `remix` - RemixIcon (default)
- `bootstrap` - Bootstrap Icons
- `tabler` - Tabler Icons
- `feather` - Feather Icons
- `lineawesome` - Line Awesome
- `boxicons` - Boxicons

### 2. SVG Icons (SVG)

Fetches and renders SVG content inline with sanitization:

```tsx
<Icon
  type="SVG"
  url="https://example.com/icon.svg"
  size={24}
  sanitize={true}
  className="text-primary"
/>
```

**Features:**
- Fetches SVG from URL
- Inline rendering (not as `<img>`)
- Security sanitization
- Loading and error states

### 3. Image Icons (IMAGE)

Renders icons as Next.js Image components:

```tsx
<Icon
  type="IMAGE"
  src="/assets/icons/custom-icon.png"
  alt="Custom Icon"
  width={24}
  height={24}
  className="rounded"
/>
```

### 4. Component Icons (ICON_COMPONENT)

Renders React components as icons:

```tsx
<Icon
  type="ICON_COMPONENT"
  component={MyCustomIconComponent}
  props={{ variant: 'primary' }}
  size={24}
/>
```

## Usage in Components

### Primary Button with Icons

The `PrimaryButton` component now supports icons:

```tsx
<PrimaryButton
  icon={{
    type: 'FONT_ICON',
    iconClass: 'ri-user-add-line',
    library: 'remix'
  }}
  iconPosition="left"
>
  Add User
</PrimaryButton>
```

### Global Page Header

Icons are automatically handled in the `GlobalPageHeader`:

```tsx
<GlobalPageHeader
  title="User Management"
  actions={[
    {
      label: "Add New User",
      icon: "ri-user-add-line",
      href: "/user-management/create"
    }
  ]}
/>
```

## API Reference

### Icon Props

```typescript
type IconProps = SVGIconProps | ImageIconProps | ComponentIconProps | FontIconProps;

interface BaseIconProps {
  className?: string;
  size?: number | string;
  color?: string;
  'aria-label'?: string;
  'aria-hidden'?: boolean;
  title?: string;
}
```

### SVG Icon Props

```typescript
interface SVGIconProps extends BaseIconProps {
  type: 'SVG';
  url: string;
  sanitize?: boolean; // default: true
}
```

### Font Icon Props

```typescript
interface FontIconProps extends BaseIconProps {
  type: 'FONT_ICON';
  iconClass: string;
  library?: 'remix' | 'bootstrap' | 'tabler' | 'feather' | 'lineawesome' | 'boxicons';
}
```

### Image Icon Props

```typescript
interface ImageIconProps extends BaseIconProps {
  type: 'IMAGE';
  src: string;
  alt: string;
  width?: number;
  height?: number;
}
```

### Component Icon Props

```typescript
interface ComponentIconProps extends BaseIconProps {
  type: 'ICON_COMPONENT';
  component: React.ComponentType<any>;
  props?: Record<string, any>;
}
```

## Security

### SVG Sanitization

The system includes built-in SVG sanitization that removes:
- Script tags
- Event handlers (onclick, onload, etc.)
- JavaScript URLs
- Other potentially dangerous content

```typescript
const sanitizeSVG = (svgContent: string): string => {
  return svgContent
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '')
    .replace(/on\w+='[^']*'/gi, '')
    .replace(/javascript:/gi, '');
};
```

## Best Practices

1. **Use Font Icons for UI Elements**: Prefer font icons for common UI elements (buttons, navigation)
2. **Use SVG for Custom Graphics**: Use SVG for custom illustrations or complex graphics
3. **Use Images for Photos/Raster**: Use images for photographs or raster graphics
4. **Provide Alt Text**: Always provide meaningful alt text for accessibility
5. **Consistent Sizing**: Use consistent sizing across similar UI elements
6. **Performance**: Consider using font icons for better performance in icon-heavy UIs

## Migration Guide

### From Direct Font Icons

**Before:**
```tsx
<i className="ri-user-add-line text-lg"></i>
```

**After:**
```tsx
<Icon
  type="FONT_ICON"
  iconClass="ri-user-add-line"
  library="remix"
  size={18}
/>
```

### From Image Tags

**Before:**
```tsx
<img src="/icon.svg" alt="Icon" width="24" height="24" />
```

**After:**
```tsx
<Icon
  type="SVG"
  url="/icon.svg"
  size={24}
  aria-label="Icon"
/>
```

## Testing

Use the `/icon-test` route to test and verify icon functionality:

```bash
# Navigate to test page
http://localhost:3001/icon-test
```

## Troubleshooting

### Icons Not Displaying

1. Check that the icon library CSS is loaded
2. Verify the icon class name is correct
3. Check network requests for SVG URLs
4. Ensure proper TypeScript types are used

### SVG Loading Issues

1. Check CORS headers for external SVG URLs
2. Verify SVG content is valid
3. Check browser console for fetch errors

### Performance Issues

1. Consider using font icons instead of SVGs for repeated icons
2. Implement icon caching for frequently used SVGs
3. Use appropriate image optimization for image icons
