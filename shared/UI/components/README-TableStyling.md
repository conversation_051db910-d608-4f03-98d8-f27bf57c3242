# Dark Theme Table Styling Guide

This document outlines the updated dark theme styling for tables and the new status/action type badge components.

## Table Styling Updates

### Table Header (thead)
- **Font**: Rubik, 600 weight (font-semibold), 16px size
- **Color**: White (text-white)
- **Line height**: 100% (leading-none)
- **Letter spacing**: 0%

### Table Body (tbody)
- **Text Color**: #999999 (text-gray-400)

### Border Radius
- **Table headers**: 8px border-radius maintained on thead elements

## New Badge Components

### StatusBadge Component

A reusable status badge component with dot indicator following the dark theme design system.

#### Usage

```tsx
import { StatusBadge } from "@/shared/UI/components";

// Basic usage
<StatusBadge status="completed" />
<StatusBadge status="failed" />
<StatusBadge status="pending" />

// With custom styling
<StatusBadge 
  status="active" 
  variant="success"
  showDot={true}
  className="custom-class"
/>
```

#### Status Variants

**Success/Win/Active states:**
- Background: rgba(65, 136, 118, 0.2) (#41887633)
- Text color: #21CE9E
- Dot color: #21CE9E
- Statuses: success, win, active, completed, won

**Failed/Loss/Inactive states:**
- Background: #5B2424
- Text color: #5B2424
- Dot color: #5B2424
- Statuses: failed, loss, inactive, lost, cancelled, void

**Pending/Processing states:**
- Background: rgba(255, 165, 0, 0.2) (#FFA50033)
- Text color: #FFA500
- Dot color: #FFA500
- Statuses: pending, processing, refunded

#### Props

```tsx
interface StatusBadgeProps {
  status: string;                    // The status value to display
  variant?: StatusVariant;           // Optional custom variant override
  className?: string;                // Additional CSS classes
  showDot?: boolean;                 // Whether to show the status dot (default: true)
  dotColor?: string;                 // Custom dot color override
  backgroundColor?: string;          // Custom background color override
  textColor?: string;                // Custom text color override
}
```

### ActionTypeBadge Component

A reusable action type badge component with orange color scheme.

#### Usage

```tsx
import { ActionTypeBadge } from "@/shared/UI/components";

<ActionTypeBadge actionType="deposit" />
<ActionTypeBadge actionType="withdrawal" />
<ActionTypeBadge actionType="bet" />
```

#### Styling
- **Background**: rgba(255, 165, 0, 0.2) (#FFA50033)
- **Text color**: #FFA500
- **Container**: 4px border-radius, 2px vertical padding, 12px horizontal padding
- **Font**: Rubik, 400 weight, 14px size, 100% line height

#### Props

```tsx
interface ActionTypeBadgeProps {
  actionType: string;                // The action type value to display
  className?: string;                // Additional CSS classes
  backgroundColor?: string;          // Custom background color override
  textColor?: string;                // Custom text color override
}
```

## Table Column Implementation

### Status Column Example

```tsx
{
  key: "status",
  title: "Status",
  sortable: true,
  width: "140px",
  render: (value, record) => (
    <StatusBadge status={value || record.status || "unknown"} />
  )
}
```

### Action Type Column Example

```tsx
{
  key: "actionType",
  title: "Action Type",
  sortable: true,
  width: "120px",
  render: (value, record) => (
    <ActionTypeBadge actionType={value || record.actionType || "unknown"} />
  )
}
```

## CSS Variables

The following CSS variables are available for custom styling:

```css
:root {
  --status-success-bg: rgba(65, 136, 118, 0.2);
  --status-success-text: #21CE9E;
  --status-failed-bg: #5B2424;
  --status-failed-text: #FB3D32;
  --action-type-bg: rgba(255, 165, 0, 0.2);
  --action-type-text: #FFA500;
}
```

## Migration Guide

### From Old Status Styling

**Before:**
```tsx
<span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
  {status}
</span>
```

**After:**
```tsx
<StatusBadge status={status} />
```

### From Old Action Type Styling

**Before:**
```tsx
<span className={`font-medium capitalize ${getActionTypeColor(actionType)}`}>
  {actionType}
</span>
```

**After:**
```tsx
<ActionTypeBadge actionType={actionType} />
```

## Benefits

1. **Consistency**: Standardized styling across all tables
2. **Maintainability**: Centralized styling logic
3. **Flexibility**: Easy customization through props
4. **Dark Theme**: Optimized for the dark theme design system
5. **Performance**: Reusable components reduce code duplication
6. **Accessibility**: Proper semantic structure and ARIA support
