// Export all global reusable components from category directories
export { ActionTypeBadge, SpkBadge, StatusBadge } from '../badges';
export { PrimaryButton, SpkPrimaryButton } from '../buttons';
export { SpkFormInput, SpkFormSelect, SpkFormActions } from '../forms';
export { SpkAdvancedFilters } from '../filters';
export type { FilterDefinition } from '../filters';
export { SpkAlert, SpkErrorMessage } from '../notifications';
export { SpkLoadingSpinner } from '../loading';
export { SpkTable, SpkPagination, EnhancedPagination } from '../tables';

// Export global components from category-based subdirectories
export { GlobalFilterSection } from '../filters';

// Export filter theming system
export { FILTER_THEMES, getFilterTheme, createCustomTheme } from '../filters';
export type { FilterSectionTheme, FilterThemeName } from '../filters';
export { GlobalPageHeader } from '../headers';
export { GlobalDataTable } from '../tables';

// Export reusable card components
export { GridCard, StatisticsCard, SummaryCard, SportsCategoryCard, ReportSummaryCards } from '../cards';
export type { GridCardProps, StatisticsCardProps, SummaryCardProps, SportsCategoryCardProps, ReportSummaryCardsProps, ReportSummaryCardData } from '../cards';

// Export reusable modal components
export * from '../modals';

// Export user management components
export { UserProfileHeader, UserStatisticsSection, FinancialSummaryCard } from '../user-management';
export type { UserProfileHeaderProps, UserStatisticsSectionProps, FinancialSummaryCardProps } from '../user-management';

export { default as UserSelectionDropdown } from './UserSelectionDropdown';

// Indicators
export { default as SpkLegendIndicator } from './SpkLegendIndicator';

// Navigation
export * from '../navigation';

// Forms
export * from '../forms';
export { UnifiedAuthenticationUI } from '../forms/SignInFormUI';

// Layouts
export * from '../layouts';

// Notifications
export * from '../notifications';

// Icons
export { Icon, UserManagementIcon } from './icons';

// Skeletons
export { TableSkeleton, CardSkeleton, PageSkeleton, FormSkeleton, AuthenticationSkeleton, UserStatisticsSkeleton, ModalSkeleton, NavigationSkeleton, SportsbookSkeleton } from '../skeletons';
export type { TableSkeletonProps, CardSkeletonProps, PageSkeletonProps, FormSkeletonProps, AuthenticationSkeletonProps, UserStatisticsSkeletonProps, ModalSkeletonProps, NavigationSkeletonProps, SportsbookSkeletonProps } from '../skeletons';

// Popovers
export * from '../popovers';

// Wrappers
export * from '../wrappers';

// Sportsbook
export * from '../sportsbook';

// Toggle Switches
export { default as AccessToggleSwitch } from './AccessToggleSwitch';

// Utility Components
export { default as CopyToClipboard } from './CopyToClipboard';

// Export types from category directories
export type * from '../badges';
export type * from '../buttons';
export type * from '../notifications';
export type * from '../loading';
export type * from '../tables';

// Export global component types
export type { GlobalFilterSectionProps } from '../filters';
export type { GlobalPageHeaderAction, GlobalPageHeaderProps } from '../headers';
export type { GlobalDataTableProps } from '../tables';

// Indicator types
export type { SpkLegendIndicatorProps } from './SpkLegendIndicator';

// Icon types
export type {
    BaseIconProps, ComponentIconProps,
    FontIconProps, IconProps, IconType, ImageIconProps, SVGIconProps,
    UserManagementActionType, UserManagementIconTheme, UserManagementIconProps
} from './icons';

