// shared/UI/components/UserSelectionDropdown.tsx
'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { SpkLoadingSpinner } from '@/shared/UI/components';
import { useUserListQuery } from '@/shared/query/useUserManagementQuery';
import { UserSelectionOption } from '@/shared/types/user-management-types';

interface UserSelectionDropdownProps {
  value?: string;
  onChange: (_userInfo: UserSelectionOption | null) => void;
  label?: string;
  helpText?: string;
  required?: boolean;
  maxUsers?: number;
  placeholder?: string;
  disabled?: boolean;
}

const UserSelectionDropdown: React.FC<UserSelectionDropdownProps> = ({
  value = '',
  onChange,
  label = 'Select User',
  helpText,
  required = false,
  maxUsers = 50,
  placeholder = 'Search and select a user...',
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserSelectionOption | null>(null);

  // Fetch users with search functionality
  const { data: userListResponse, isLoading, isError } = useUserListQuery({
    size: maxUsers,
    page: 1,
    search: searchTerm.trim() || undefined
  });

  // Convert user list to selection options
  const userOptions = useMemo(() => {
    if (!userListResponse?.data) return [];

    return userListResponse.data.map(user => {
      // Handle potential field name variations for walletId
      const walletId = user.walletId || (user as any).walletid || (user as any).wallet_id || '';

      return {
        value: user.id.toString(),
        label: user.userName,
        walletId,
        email: user.email || '',
        active: user.active
      };
    });
  }, [userListResponse]);

  // Update selected user when value prop changes
  useEffect(() => {
    if (value && userOptions.length > 0) {
      const foundUser = userOptions.find(option => option.value === value);
      if (foundUser && (!selectedUser || foundUser.value !== selectedUser.value)) {
        setSelectedUser(foundUser);
      }
    } else if (!value && selectedUser) {
      setSelectedUser(null);
    }
  }, [value, userOptions, selectedUser]);

  const handleUserSelect = (userOption: UserSelectionOption) => {
    setSelectedUser(userOption);
    setIsOpen(false);
    setSearchTerm('');
    onChange(userOption);
  };

  const handleClear = () => {
    setSelectedUser(null);
    setSearchTerm('');
    onChange(null);
  };

  const filteredOptions = useMemo(() => {
    if (!searchTerm.trim()) return userOptions;

    const term = searchTerm.toLowerCase();
    return userOptions.filter(option =>
      option.label.toLowerCase().includes(term) ||
      option.email.toLowerCase().includes(term) ||
      option.value.includes(term)
    );
  }, [userOptions, searchTerm]);

  return (
    <div className="relative">
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {/* Dropdown Button */}
      <div className="relative">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className={`
            w-full px-3 py-2 text-left border rounded-[4px] shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
            ${disabled
              ? 'bg-gray-100 dark:bg-gray-800 text-gray-500 cursor-not-allowed'
              : 'bg-white bg-elevated text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600'
            }
            ${isOpen ? 'border-blue-500 ring-2 ring-blue-500' : 'border-border-secondary'}
          `}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center min-w-0 flex-1">
              {selectedUser ? (
                <div className="flex items-center gap-2 min-w-0">
                  <div className="w-6 h-6 rounded-[4px] bg-blue-100 dark:bg-blue-800 flex items-center justify-center flex-shrink-0">
                    <i className="ri-user-line text-blue-600 dark:text-blue-300 text-xs"></i>
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="font-medium truncate">{selectedUser.label}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                      ID: {selectedUser.value}
                    </div>
                  </div>
                </div>
              ) : (
                <span className="text-gray-500 dark:text-gray-400">{placeholder}</span>
              )}
            </div>

            <div className="flex items-center gap-1 ml-2">
              {selectedUser && !disabled && (
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    handleClear();
                  }}
                  className="p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded cursor-pointer"
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      e.stopPropagation();
                      handleClear();
                    }
                  }}
                >
                  <i className="ri-close-line text-gray-400 text-sm"></i>
                </div>
              )}
              <i className={`ri-arrow-down-s-line text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}></i>
            </div>
          </div>
        </button>

        {/* Dropdown Menu */}
        {isOpen && !disabled && (
          <div className="absolute z-50 w-full mt-1 bg-white bg-elevated border border-border-secondary rounded-md shadow-lg max-h-60 overflow-hidden">
            {/* Search Input */}
            <div className="p-2 border-b border-border-secondary">
              <div className="relative">
                <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search users..."
                  className="w-full pl-9 pr-3 py-2 text-sm border border-border-secondary rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-elevated dark:text-white"
                  autoFocus
                />
              </div>
            </div>

            {/* Options List */}
            <div className="max-h-48 overflow-y-auto">
              {isLoading ? (
                <div className="flex items-center justify-center py-4">
                  <SpkLoadingSpinner size="sm" className="mr-2" />
                  <span className="text-sm text-gray-500">Loading users...</span>
                </div>
              ) : isError ? (
                <div className="p-3 text-center text-red-500 text-sm">
                  <i className="ri-error-warning-line mr-1"></i>
                  Failed to load users
                </div>
              ) : filteredOptions.length === 0 ? (
                <div className="p-3 text-center text-gray-500 text-sm">
                  <i className="ri-user-search-line mr-1"></i>
                  {searchTerm ? 'No users found matching your search' : 'No users available'}
                </div>
              ) : (
                filteredOptions.map((option) => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => handleUserSelect(option)}
                    className={`
                      w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-600
                      ${selectedUser?.value === option.value ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                    `}
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center flex-shrink-0">
                        <i className="ri-user-line text-gray-600 dark:text-gray-300 text-xs"></i>
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="font-medium text-gray-900 dark:text-white truncate">
                          {option.label}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          ID: {option.value} • {option.email}
                        </div>
                      </div>
                      {!option.active && (
                        <span className="px-2 py-1 text-xs bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300 rounded">
                          Inactive
                        </span>
                      )}
                      {selectedUser?.value === option.value && (
                        <i className="ri-check-line text-blue-600 dark:text-blue-400"></i>
                      )}
                    </div>
                  </button>
                ))
              )}
            </div>
          </div>
        )}
      </div>

      {/* Help Text */}
      {helpText && (
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          {helpText}
        </p>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default UserSelectionDropdown;
