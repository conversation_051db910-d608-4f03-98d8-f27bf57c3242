"use client";

import React, { Fragment } from "react";
import PrimaryButton from '../buttons/PrimaryButton';
import AuthenticationBackgroundWrapper from '../wrappers/AuthenticationBackgroundWrapper';

export interface SignInFormUIProps {
  // Form state
  email: string;
  password: string;
  showPassword: boolean;

  // Validation state
  emailError: string;
  passwordError: string;

  // Authentication state
  isPending: boolean;
  isError: boolean;
  error: Error | null;
  hasHydrated: boolean;

  // Handlers
  setEmail: (email: string) => void;
  setPassword: (password: string) => void;
  setShowPassword: (show: boolean) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;

  // Background images
  backgroundImages: string[];
}

// Unified Authentication Component Props
export interface UnifiedAuthenticationUIProps {
  // Page type
  pageType: 'sign-in' | '2-step-verification';

  // Common authentication state
  isPending: boolean;
  isError: boolean;
  error: Error | null;
  hasHydrated: boolean;
  backgroundImages: string[];

  // Sign-in specific props
  email?: string;
  password?: string;
  showPassword?: boolean;
  emailError?: string;
  passwordError?: string;
  setEmail?: (email: string) => void;
  setPassword?: (password: string) => void;
  setShowPassword?: (show: boolean) => void;
  handleSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;

  // 2-step verification specific props
  inputValues?: Record<string, string>;
  setRef?: (element: HTMLInputElement | null, index: number) => void;
  handleChange?: (index: number, value: string) => void;
  handleKeyDown?: (index: number, e: React.KeyboardEvent<HTMLInputElement>) => void;
  handlePaste?: (index: number, e: React.ClipboardEvent<HTMLInputElement>) => void;
  handleVerifyPin?: (e: React.MouseEvent<HTMLAnchorElement | HTMLButtonElement>) => void;
  PIN_LENGTH?: number;
  tempUserFor2FA?: { id: number; email: string } | null;
  loginMessage?: string | null;
}

/**
 * Pure presentational component for sign-in form UI
 * Renders the improved design with golden theme, horizontal split layout, and CSS animations
 * This component is completely stateless and only handles presentation
 */
export const SignInFormUI: React.FC<SignInFormUIProps> = ({
  email,
  password,
  showPassword,
  emailError,
  passwordError,
  isPending,
  isError,
  error,
  hasHydrated,
  setEmail,
  setPassword,
  setShowPassword,
  handleSubmit,
  backgroundImages,
}) => {
  // Show loading state while hydrating to prevent hydration mismatch
  if (!hasHydrated) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center bg-background">
        <div className="bg-elevated rounded-lg border border-border-primary p-12 text-center animate-pulse">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-[#404040] border-t-primary mx-auto"></div>
          <p className="mt-4 text-text-secondary font-rubik">Loading authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <Fragment>
      <AuthenticationBackgroundWrapper backgroundImages={backgroundImages}>

        {/* Main container with horizontal split layout */}
        <div
          className="w-[777px] h-[476px] rounded-lg px-[3rem] flex"
          style={{
            backgroundImage: "url(/assets/login/bgborder.svg)",
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        >
          {/* Right side - Sign-in form section (60%) */}
          <div className="w-[60%] h-full ml-auto gap-[20px] flex p-6 mt-3 flex flex-col justify-center">
            {/* Login Title */}
            <div className="text-center mb-2 flex flex-col gap-[8px]">
              <h1
                className="text-[#E1B649] font-rubik font-bold text-center"
                style={{
                  fontSize: '24px',
                  lineHeight: '24px',
                  letterSpacing: '0%',
                  color: '#E1B649',
                  fontWeight: '900 !important'
                }}
              >
                Login
              </h1>

              <p
                className="text-gray-300 font-rubik font-normal text-center"
                style={{
                  fontSize: '16px',
                  lineHeight: '16.64px',
                  letterSpacing: '0%'
                }}
              >
                Enter your login details
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4 max-w-sm mx-auto w-full">
              {/* Email Input */}
              <div>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Email"
                  required
                  className="w-full border-0 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-[#E1B649]/50 transition-all duration-200 font-rubik font-normal"
                  style={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    padding: '20px 12px',
                    borderRadius: '8px',
                    fontSize: '16px',
                    lineHeight: '16px',
                    letterSpacing: '0%'
                  }}
                />
                {emailError && (
                  <div className="text-red-400 text-xs mt-1">{emailError}</div>
                )}
              </div>

              {/* Password Input */}
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Password"
                  required
                  className="w-full border-0 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-[#E1B649]/50 transition-all duration-200 font-rubik font-normal capitalize"
                  style={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    padding: '20px 12px',
                    borderRadius: '8px',
                    fontSize: '16px',
                    lineHeight: '16px',
                    letterSpacing: '0%'
                  }}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-white transition-colors duration-200"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                      <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  )}
                </button>
                {passwordError && (
                  <div className="text-red-400 text-xs mt-1">{passwordError}</div>
                )}
              </div>

              {/* Login Button */}
              <div className="pt-2">
                <PrimaryButton
                  type="submit"
                  disabled={isPending}
                  loading={isPending}
                  loadingText="Signing in..."
                  fullWidth={true}
                >
                  Login
                </PrimaryButton>
              </div>

              {/* Error Message */}
              {isError && (
                <div className="bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-2 rounded-md text-sm text-center">
                  {error?.message || 'An error occurred during sign in. Please try again.'}
                </div>
              )}
            </form>
          </div>
        </div>
      </AuthenticationBackgroundWrapper>
    </Fragment>
  );
};

/**
 * Render Sign-In Form Content
 */
const renderSignInForm = (props: UnifiedAuthenticationUIProps, pageContent: any) => {
  const {
    email = '',
    password = '',
    showPassword = false,
    emailError = '',
    passwordError = '',
    setEmail,
    setPassword,
    setShowPassword,
    handleSubmit,
    isPending,
    isError,
    error
  } = props;

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-sm mx-auto w-full">
      {/* Email Input */}
      <div>
        <input
          type="email"
          id="email"
          name="email"
          value={email}
          onChange={(e) => setEmail?.(e.target.value)}
          placeholder="Email"
          required
          className="w-full border-0 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-[#E1B649]/50 transition-all duration-200 font-rubik font-normal"
          style={{
            background: '#2C2C2F', // Using form-input color from tailwind config
            padding: '20px 12px',
            borderRadius: '8px',
            fontSize: '16px',
            lineHeight: '16px',
            letterSpacing: '0%'
          }}
        />
        {emailError && (
          <div className="text-red-400 text-xs mt-1">{emailError}</div>
        )}
      </div>

      {/* Password Input */}
      <div className="relative">
        <input
          type={showPassword ? "text" : "password"}
          id="password"
          name="password"
          value={password}
          onChange={(e) => setPassword?.(e.target.value)}
          placeholder="Password"
          required
          className="w-full border-0 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-[#E1B649]/50 transition-all duration-200 font-rubik font-normal capitalize"
          style={{
            background: '#2C2C2F', // Using form-input color from tailwind config
            padding: '20px 12px',
            borderRadius: '8px',
            fontSize: '16px',
            lineHeight: '16px',
            letterSpacing: '0%'
          }}
        />
        <button
          type="button"
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-white transition-colors duration-200"
          onClick={() => setShowPassword?.(!showPassword)}
        >
          {showPassword ? (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
              <path strokeLinecap="round" strokeLinejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
              <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
              <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          )}
        </button>
        {passwordError && (
          <div className="text-red-400 text-xs mt-1">{passwordError}</div>
        )}
      </div>

      {/* Login Button */}
      <div className="pt-2">
        <PrimaryButton
          type="submit"
          disabled={isPending}
          loading={isPending}
          loadingText={pageContent.loadingText}
          fullWidth={true}
        >
          {pageContent.buttonText}
        </PrimaryButton>
      </div>

      {/* Error Message */}
      {isError && (
        <div className="bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-2 rounded-md text-sm text-center">
          {error?.message || 'An error occurred during sign in. Please try again.'}
        </div>
      )}
    </form>
  );
};

/**
 * Render 2-Step Verification Form Content
 */
const renderTwoStepForm = (props: UnifiedAuthenticationUIProps, pageContent: any) => {
  const {
    inputValues = {},
    setRef,
    handleChange,
    handleKeyDown,
    handlePaste,
    handleVerifyPin,
    PIN_LENGTH = 6,
    isPending,
    isError,
    error
  } = props;

  return (
    <div className="space-y-4 max-w-sm mx-auto w-full">
      {/* PIN Input Section */}
      <div className="mb-6">
        <div className="grid grid-cols-6 gap-2">
          {Array.from({ length: PIN_LENGTH }).map((_, index) => (
            <div key={index}>
              <input
                type="text"
                className="w-full h-12 text-center text-white border-0 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#E1B649]/50 transition-all duration-200 font-rubik font-normal"
                id={`pin-input-${index}`}
                value={inputValues[index] || ''}
                onChange={(e) => handleChange?.(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown?.(index, e)}
                onPaste={(e) => handlePaste?.(index, e)}
                maxLength={1}
                inputMode="text"
                pattern="[a-zA-Z0-9]*"
                autoComplete="off"
                ref={(el: HTMLInputElement | null) => setRef?.(el, index)}
                style={{
                  background: '#2C2C2F', // Using form-input color from tailwind config
                  color: 'white', // Ensure text color is white
                  fontSize: '16px',
                  lineHeight: '16px',
                  letterSpacing: '0%'
                }}
              />
            </div>
          ))}
        </div>

        {/* Error Message */}
        {isError && (
          <div className="bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-3 rounded-md mt-4" role="alert">
            {error?.message || "Failed to verify PIN."}
          </div>
        )}
      </div>

      {/* Verify Button */}
      <div className="pt-2">
        <PrimaryButton
          onClick={() => {
            // Create a minimal event object with preventDefault method
            const mockEvent = {
              preventDefault: () => { }
            } as React.MouseEvent<HTMLButtonElement>;

            handleVerifyPin?.(mockEvent);
          }}
          disabled={isPending}
          loading={isPending}
          loadingText={pageContent.loadingText}
          fullWidth={true}
        >
          {pageContent.buttonText}
        </PrimaryButton>
      </div>
    </div>
  );
};

/**
 * Unified Authentication UI Component
 * Handles both sign-in and 2-step verification pages with dynamic content
 */
export const UnifiedAuthenticationUI: React.FC<UnifiedAuthenticationUIProps> = (props) => {
  const {
    pageType,
    isPending: _isPending,
    isError: _isError,
    error: _error,
    hasHydrated,
    backgroundImages,
  } = props;

  // Show loading state while hydrating to prevent hydration mismatch
  if (!hasHydrated) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center bg-background">
        <div className="bg-elevated rounded-lg border border-border-primary p-12 text-center animate-pulse">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-[#404040] border-t-primary mx-auto"></div>
          <p className="mt-4 text-text-secondary font-rubik">Loading authentication...</p>
        </div>
      </div>
    );
  }

  // Get dynamic content based on page type
  const getPageContent = () => {
    if (pageType === 'sign-in') {
      return {
        title: 'Login',
        subtitle: 'Enter your login details',
        buttonText: 'Login',
        loadingText: 'Signing in...'
      };
    } else {
      return {
        title: 'Verification Code',
        subtitle: props.loginMessage || `Enter the 8 digit code sent to ${props.tempUserFor2FA?.email}.`,
        buttonText: 'Verify',
        loadingText: 'Verifying...'
      };
    }
  };

  const pageContent = getPageContent();

  return (
    <Fragment>
      <AuthenticationBackgroundWrapper backgroundImages={backgroundImages}>
        {/* Main container with horizontal split layout */}
        <div
          className="w-[777px] h-[476px] rounded-lg px-[3rem] flex"
          style={{
            backgroundImage: "url(/assets/login/bgborder.svg)",
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        >
          {/* Right side - Form section (60%) */}
          <div className="w-[60%] h-full ml-auto gap-[20px] flex p-6 mt-3 flex flex-col justify-center">
            {/* Dynamic Title */}
            <div className="text-center mb-2 flex flex-col gap-[8px]">
              <h1
                className="text-[#E1B649] font-rubik font-bold text-center"
                style={{
                  fontSize: pageType === 'sign-in' ? '24px' : '24px',
                  lineHeight: '24px',
                  letterSpacing: '0%',
                  color: '#E1B649',
                  fontWeight: '900 !important'
                }}
              >
                {pageContent.title}
              </h1>

              <p
                className="text-gray-300 font-rubik font-normal text-center"
                style={{
                  fontSize: '16px',
                  lineHeight: '16.64px',
                  letterSpacing: '0%'
                }}
              >
                {pageContent.subtitle}
              </p>
            </div>

            {/* Dynamic Form Content */}
            {pageType === 'sign-in' ? renderSignInForm(props, pageContent) : renderTwoStepForm(props, pageContent)}
          </div>
        </div>
      </AuthenticationBackgroundWrapper>
    </Fragment>
  );
};

export default SignInFormUI;
