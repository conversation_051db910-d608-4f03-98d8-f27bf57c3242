"use client";

import React from 'react';
import { SpkLoadingSpinner } from '../components';

export interface SummaryCardProps {
  title: string;
  value: string | number;
  currency?: string;
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'primary' | 'info';
  icon?: string;
  iconComponent?: React.ReactNode;
  isLoading?: boolean;
  className?: string;
  backgroundType?: 'general' | 'cashier' | 'custom';
  customBackground?: string;
  showBorder?: boolean;
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    label?: string;
  };
}

/**
 * SummaryCard Component
 * 
 * A reusable card component for displaying summary statistics and metrics.
 * Consolidates all the different card patterns used across the application.
 * 
 * Features:
 * - Multiple background types (general reports, cashier reports, custom)
 * - Texture background support with semantic classes
 * - Icon support (string class or React component)
 * - Loading states with spinner
 * - Trend indicators with positive/negative styling
 * - Multiple size variants
 * - Clickable cards with hover effects
 * - Full dark theme compatibility
 * - TypeScript interfaces for type safety
 */
const SummaryCard: React.FC<SummaryCardProps> = ({
  title,
  value,
  currency,
  variant = 'default',
  icon,
  iconComponent,
  isLoading = false,
  className = '',
  backgroundType = 'general',
  customBackground,
  showBorder = true,
  size = 'md',
  onClick,
  subtitle,
  trend
}) => {
  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      return currency ? `${currency}${val.toLocaleString()}` : val.toLocaleString();
    }
    return val;
  };

  const getBackgroundClasses = () => {
    if (customBackground) return customBackground;
    
    switch (backgroundType) {
      case 'cashier':
        return 'bg-card-cashier-texture';
      case 'general':
      default:
        return 'bg-card-general-texture';
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'success':
        return {
          iconBg: 'bg-green-500/20',
          iconColor: 'text-green-500',
          valueColor: 'text-green-600'
        };
      case 'warning':
        return {
          iconBg: 'bg-yellow-500/20',
          iconColor: 'text-yellow-500',
          valueColor: 'text-yellow-600'
        };
      case 'danger':
        return {
          iconBg: 'bg-red-500/20',
          iconColor: 'text-red-500',
          valueColor: 'text-red-600'
        };
      case 'primary':
        return {
          iconBg: 'bg-blue-500/20',
          iconColor: 'text-blue-500',
          valueColor: 'text-blue-600'
        };
      case 'info':
        return {
          iconBg: 'bg-purple-500/20',
          iconColor: 'text-purple-500',
          valueColor: 'text-purple-600'
        };
      default:
        return {
          iconBg: 'bg-gray-500/20',
          iconColor: 'text-gray-500',
          valueColor: 'text-text-muted'
        };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          padding: 'p-3',
          titleSize: 'text-xs',
          valueSize: 'text-lg',
          iconSize: 'text-sm',
          iconPadding: 'p-2'
        };
      case 'lg':
        return {
          padding: 'p-6',
          titleSize: 'text-base',
          valueSize: 'text-3xl',
          iconSize: 'text-xl',
          iconPadding: 'p-4'
        };
      case 'md':
      default:
        return {
          padding: 'p-4',
          titleSize: 'text-sm',
          valueSize: 'text-2xl',
          iconSize: 'text-lg',
          iconPadding: 'p-3'
        };
    }
  };

  const variantClasses = getVariantClasses();
  const sizeClasses = getSizeClasses();

  const cardClasses = [
    getBackgroundClasses(),
    sizeClasses.padding,
    'rounded-lg',
    showBorder ? 'border border-gray-200 dark:border-gray-700' : '',
    onClick ? 'cursor-pointer hover:shadow-lg hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-300 group' : '',
    className
  ].filter(Boolean).join(' ');

  if (isLoading) {
    return (
      <div className={cardClasses}>
        <div className="flex items-center justify-center h-24">
          <SpkLoadingSpinner size="md" />
        </div>
      </div>
    );
  }

  return (
    <div className={cardClasses} onClick={onClick}>
      <div className="flex items-center justify-between mb-4">
        <h6 className={`${sizeClasses.titleSize} font-medium text-text-secondary group-hover:text-text-muted transition-colors`}>
          {title}
        </h6>
        {(icon || iconComponent) && (
          <div className={`${sizeClasses.iconPadding} rounded-full ${variantClasses.iconBg} group-hover:scale-110 transition-transform duration-200`}>
            {iconComponent || (
              <i className={`${icon} ${sizeClasses.iconSize} ${variantClasses.iconColor}`}></i>
            )}
          </div>
        )}
      </div>
      
      <div className="mb-3">
        <span className={`${sizeClasses.valueSize} font-bold ${variantClasses.valueColor} group-hover:scale-105 transition-transform duration-200 inline-block`}>
          {formatValue(value)}
        </span>
        {subtitle && (
          <div className={`${sizeClasses.titleSize} text-text-secondary mt-1`}>
            {subtitle}
          </div>
        )}
      </div>

      {trend && (
        <div className={`${sizeClasses.titleSize} text-text-secondary flex items-center gap-1`}>
          <span className={trend.isPositive ? 'text-green-500' : 'text-red-500'}>
            <i className={`ri-arrow-${trend.isPositive ? 'up' : 'down'}-line`}></i>
            {Math.abs(trend.value)}%
          </span>
          {trend.label && <span>{trend.label}</span>}
        </div>
      )}
    </div>
  );
};

export default SummaryCard;
