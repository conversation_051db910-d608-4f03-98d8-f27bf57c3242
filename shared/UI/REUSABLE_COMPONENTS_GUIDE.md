# Reusable UI Components Guide

This guide documents the new reusable UI components created to eliminate code duplication and standardize component patterns across the application.

## 🎯 Overview

The reusable component system consolidates duplicate patterns found throughout the codebase into standardized, reusable components that follow our established architecture patterns:

- **Pure UI Components**: All components are presentational and accept props
- **TypeScript Interfaces**: Full type safety with comprehensive prop interfaces
- **Dark Theme Compatible**: Semantic CSS classes and dark theme support
- **Consistent Styling**: Follows the established design system
- **Performance Optimized**: Efficient rendering and minimal re-renders

## 📦 Component Categories

### 1. Card Components (`shared/UI/cards/`)

#### SummaryCard
**Purpose**: Consolidates all report summary card patterns (bet reports, financial reports, user details)

**Features**:
- Multiple background types (general, cashier, custom)
- Texture background support with semantic classes
- Icon support (string class or React component)
- Loading states with spinner
- Trend indicators with positive/negative styling
- Multiple size variants (sm, md, lg)
- Clickable cards with hover effects

**Usage**:
```tsx
import { SummaryCard } from '@/shared/UI/components';

// Basic usage
<SummaryCard
  title="Total Bets"
  value={1234}
  currency="$"
  icon="ri-bar-chart-line"
  backgroundType="general"
/>

// With trend indicator
<SummaryCard
  title="Revenue"
  value={50000}
  currency="$"
  variant="success"
  trend={{ value: 12.5, isPositive: true, label: "vs last month" }}
  backgroundType="cashier"
/>
```

#### StatisticsCard
**Purpose**: Consolidates dashboard-style cards (spk-cards, spk-card1, widget cards)

**Features**:
- Dashboard-style layout with icon and statistics
- Customizable colors and backgrounds
- Percentage change indicators with trend arrows
- Avatar-style icon containers
- Multiple size variants

**Usage**:
```tsx
import { StatisticsCard } from '@/shared/UI/components';

<StatisticsCard
  title="Active Users"
  value={2847}
  icon="bx bx-user"
  iconBackgroundColor="bg-primary"
  percentageChange={{ value: 8.2, isIncrease: true }}
  additionalInfo="Last 30 days"
/>
```

#### GridCard
**Purpose**: Consolidates grid layout cards (spkgridcards, spkgridmarkupcard, spktitlecards)

**Features**:
- Image support with Next.js Image optimization
- Flexible content areas (header, body, footer)
- Badge support with customizable styling
- Button/link actions with routing
- Clickable card functionality

**Usage**:
```tsx
import { GridCard } from '@/shared/UI/components';

<GridCard
  title="Product Card"
  description="Product description here"
  imageSrc="/images/product.jpg"
  href="/products/1"
  buttonText="View Details"
  badge={{ text: "New", color: "success" }}
/>
```

#### ReportSummaryCards
**Purpose**: Consolidates report summary card patterns (bet-report, cashier-report, financial-report)

**Features**:
- Data-driven approach with configurable card array
- Automatic SVG icon mapping based on card type
- Multiple background types (general reports vs cashier reports)
- Consistent text-white styling for all amounts
- Responsive grid layout with configurable columns
- Loading states with spinner
- Consistent styling matching existing card patterns
- Full dark theme compatibility
- Type-safe icon loading from appropriate directories

**Usage**:
```tsx
import { ReportSummaryCards } from '@/shared/UI/components';

// Bet Report usage
const betReportCards = [
  {
    type: 'bet-amount',
    label: 'Total Bet Amount',
    value: 125000,
    currency: '$'
  },
  {
    type: 'win-amount',
    label: 'Total Win Amount',
    value: 98000,
    currency: '$'
  },
  {
    type: 'ggr',
    label: 'GGR',
    value: 27000,
    currency: '$'
  }
];

<ReportSummaryCards
  cards={betReportCards}
  backgroundType="general"
  gridColumns={3}
  height="130px"
/>

// Cashier Report usage
const cashierReportCards = [
  {
    type: 'total-deposit',
    label: 'Total Deposit',
    value: 250000,
    currency: '$'
  },
  {
    type: 'total-withdraw',
    label: 'Total Withdraw',
    value: 180000,
    currency: '$'
  },
  {
    type: 'total-bet-placed',
    label: 'Total Bet Placed',
    value: 1250
  }
];

<ReportSummaryCards
  cards={cashierReportCards}
  backgroundType="cashier"
  gridColumns={3}
  height="130px"
/>
```

### 2. Modal Components (`shared/UI/modals/`)

#### BaseModal
**Purpose**: Provides common modal functionality that other modals can extend

**Features**:
- Portal rendering for proper z-index layering
- Backdrop click and escape key handling
- Multiple size variants and positioning options
- Customizable animations (fade, slide, scale)
- Header, body, and footer sections
- Accessibility features (focus management, ARIA attributes)

**Usage**:
```tsx
import { BaseModal } from '@/shared/UI/components';

<BaseModal
  isOpen={isModalOpen}
  onClose={() => setIsModalOpen(false)}
  title="Modal Title"
  size="md"
  animation="scale"
>
  <p>Modal content goes here</p>
</BaseModal>
```

## 🔄 Migration Guide

### Replacing Duplicate Card Components

#### Before (Multiple Implementations):
```tsx
// Old spk-cards pattern
<div className="box">
  <div className="box-body">
    <div className="flex justify-between">
      <div>
        <span className="text-textmuted">{title}</span>
        <h4 className="font-medium">{count}</h4>
      </div>
      <span className="avatar bg-primary">
        <i className={icon}></i>
      </span>
    </div>
  </div>
</div>

// Old report summary pattern
<div className="bg-card-general-texture p-4 rounded-lg border">
  <div className="text-sm text-text-secondary">{title}</div>
  <div className="text-2xl font-bold text-text-primary">{value}</div>
</div>
```

#### After (Unified Implementation):
```tsx
// Use StatisticsCard for dashboard-style cards
<StatisticsCard
  title={title}
  value={count}
  icon={icon}
  iconBackgroundColor="bg-primary"
/>

// Use SummaryCard for report summary cards
<SummaryCard
  title={title}
  value={value}
  backgroundType="general"
/>
```

### Benefits of Migration

1. **Reduced Code Duplication**: Eliminates multiple similar implementations
2. **Consistent Styling**: Standardized appearance across the application
3. **Better Maintainability**: Single source of truth for component logic
4. **Type Safety**: Comprehensive TypeScript interfaces
5. **Performance**: Optimized rendering and reduced bundle size
6. **Accessibility**: Built-in accessibility features
7. **Dark Theme**: Consistent dark theme support

## 🎨 Styling Guidelines

### Semantic CSS Classes
All components use semantic CSS classes from our dark theme system:

- `bg-card-general-texture` - General report cards
- `bg-card-cashier-texture` - Cashier/user detail cards
- `bg-background` - Main background
- `bg-elevated` - Elevated surfaces
- `text-text-primary` - Primary text color
- `text-text-secondary` - Secondary text color

### Size Variants
Consistent size variants across all components:

- `sm` - Small size for compact layouts
- `md` - Medium size (default)
- `lg` - Large size for emphasis

### Color Variants
Standardized color variants for semantic meaning:

- `default` - Standard styling
- `primary` - Primary brand color
- `success` - Success/positive states
- `warning` - Warning/caution states
- `danger` - Error/negative states
- `info` - Informational states

## 🚀 Next Steps

1. **Gradual Migration**: Replace existing duplicate components with reusable ones
2. **Testing**: Ensure all functionality is preserved during migration
3. **Documentation**: Update component documentation as patterns evolve
4. **Optimization**: Monitor performance and optimize as needed

## 📝 Contributing

When creating new components:

1. Check if existing reusable components can be extended
2. Follow the established architecture patterns
3. Include comprehensive TypeScript interfaces
4. Ensure dark theme compatibility
5. Add proper documentation and usage examples
6. Test across different screen sizes and themes
