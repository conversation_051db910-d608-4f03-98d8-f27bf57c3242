import React from 'react';

export interface ActionTypeBadgeProps {
  /** The action type value to display */
  actionType: string;
  /** Additional CSS classes */
  className?: string;
  /** Custom background color override */
  backgroundColor?: string;
  /** Custom text color override */
  textColor?: string;
}

/**
 * Map technical transaction type names to user-friendly display labels
 */
const getDisplayLabel = (actionType: string): string => {
  // const labelMap: { [key: string]: string } = {
  //   // Technical names to user-friendly labels (as specified by user requirements)
  //   'type_exchange_place_bet_cash_debit': 'withdraw',
  //   'type_deposit': 'deposit',

  //   // Additional technical name variations
  //   'type_withdrawal': 'withdraw',
  //   'type_transfer': 'transfer',
  //   'exchange_place_bet_cash_debit': 'withdraw',
  //   'exchange_place_bet_cash_credit': 'deposit',

  //   // Current mapped values from useCashierReportQuery
  //   'bet': 'bet',
  //   'win': 'win',
  //   'deposit': 'deposit',
  //   'withdrawal': 'withdraw',
  //   'transfer': 'transfer',

  //   // Fallback patterns for type_* format
  //   'type_21': 'withdraw', // place bet cash debit
  //   'type_22': 'deposit',  // place bet cash credit (win)
  //   'type_1': 'deposit',
  //   'type_2': 'withdraw',
  //   'type_3': 'transfer',

  //   // Add more mappings as needed
  // };

  //labelMap[actionType] ||
  return actionType?.replace('type_', '');
};

/**
 * Get color scheme based on action type
 */
const getActionTypeColors = (actionType: string): { backgroundColor: string; textColor: string } => {
  const displayLabel = getDisplayLabel(actionType);

  // Green variant for deposit actions (similar to success status)
  if (displayLabel === 'deposit') {
    return {
      backgroundColor: 'var(--status-success-bg, rgba(65, 136, 118, 0.2))',
      textColor: 'var(--status-success-text, #21CE9E)'
    };
  }

  // Orange variant for withdraw and other actions (default)
  return {
    backgroundColor: 'var(--action-type-bg, rgba(255, 165, 0, 0.2))',
    textColor: 'var(--action-type-text, #FFA500)'
  };
};

/**
 * ActionTypeBadge Component
 *
 * A reusable action type badge component following the dark theme design system.
 * Uses color-coded styling: green for deposits, orange for withdrawals and other actions.
 * Automatically converts technical transaction type names to user-friendly labels.
 */
const ActionTypeBadge: React.FC<ActionTypeBadgeProps> = ({
  actionType,
  className = '',
  backgroundColor,
  textColor
}) => {
  const containerClasses = [
    'inline-flex',
    'items-center',
    'px-3',
    'py-0.5',
    'rounded-[4px]',
    'font-rubik',
    'font-normal',
    'text-sm',
    'leading-none',
    className
  ].filter(Boolean).join(' ');

  // Get user-friendly display label
  const displayLabel = getDisplayLabel(actionType);

  // Get color scheme based on action type (unless overridden by props)
  const colors = getActionTypeColors(actionType);

  return (
    <div
      className={containerClasses}
      style={{
        backgroundColor: backgroundColor || colors.backgroundColor,
        color: textColor || colors.textColor
      }}
    >
      <span className="capitalize truncate">
        {displayLabel}
      </span>
    </div>
  );
};

export default ActionTypeBadge;
