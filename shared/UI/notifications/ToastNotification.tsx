// shared/UI/components/notifications/ToastNotification.tsx
import React, { useCallback, useEffect, useState } from 'react';

export interface ToastNotificationProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  onClose: (_id: string) => void;
}

const ToastNotification: React.FC<ToastNotificationProps> = ({
  id: _id,
  type,
  title,
  message,
  duration = 5000,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  const handleClose = useCallback(() => {
    setIsExiting(true);
    setTimeout(() => {
      onClose(_id);
    }, 300); // Match exit animation duration
  }, [_id, onClose]);

  useEffect(() => {
    // Show animation
    const showTimer = setTimeout(() => setIsVisible(true), 100);

    // Auto-close timer
    const closeTimer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => {
      clearTimeout(showTimer);
      clearTimeout(closeTimer);
    };
  }, [duration, handleClose]);

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return {
          bgColor: 'bg-elevated', // #272729 - Dark theme elevated background
          borderColor: 'border-green-500/30',
          iconColor: 'text-green-400',
          titleColor: 'text-white',
          messageColor: 'text-text-secondary', // #AEAEAE
          icon: 'ri-check-circle-line'
        };
      case 'error':
        return {
          bgColor: 'bg-elevated', // #272729 - Dark theme elevated background
          borderColor: 'border-red-500/30',
          iconColor: 'text-red-400',
          titleColor: 'text-white',
          messageColor: 'text-text-secondary', // #AEAEAE
          icon: 'ri-error-warning-line'
        };
      case 'warning':
        return {
          bgColor: 'bg-elevated', // #272729 - Dark theme elevated background
          borderColor: 'border-yellow-500/30',
          iconColor: 'text-yellow-400',
          titleColor: 'text-white',
          messageColor: 'text-text-secondary', // #AEAEAE
          icon: 'ri-alert-line'
        };
      case 'info':
      default:
        return {
          bgColor: 'bg-elevated', // #272729 - Dark theme elevated background
          borderColor: 'border-blue-500/30',
          iconColor: 'text-blue-400',
          titleColor: 'text-white',
          messageColor: 'text-text-secondary', // #AEAEAE
          icon: 'ri-information-line'
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <div
      className={`
        w-[380px] max-h-[120px]
        transform transition-all duration-300 ease-in-out
        ${isVisible && !isExiting ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
        ${isExiting ? 'translate-x-full opacity-0' : ''}
      `}
    >
      <div
        className={`
          ${styles.bgColor} ${styles.borderColor}
          border rounded-lg shadow-lg p-4 w-full max-h-[120px] overflow-hidden
          backdrop-blur-sm
        `}
      >
        <div className="flex items-start gap-3 h-full">
          <div className="flex-shrink-0 mt-0.5">
            <i className={`${styles.icon} ${styles.iconColor} text-lg`}></i>
          </div>
          <div className="flex-1 min-w-0 overflow-hidden">
            <h4 className={`${styles.titleColor} font-rubik font-semibold text-sm mb-1 truncate`}>
              {title}
            </h4>
            <p
              className={`${styles.messageColor} font-rubik text-sm leading-tight overflow-hidden`}
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                maxHeight: '2.5rem' // Approximately 2 lines
              }}
            >
              {message}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="flex-shrink-0 text-text-muted hover:text-white transition-colors duration-200 p-1 hover:bg-white/10 rounded"
            aria-label="Close notification"
          >
            <i className="ri-close-line text-base"></i>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ToastNotification;
