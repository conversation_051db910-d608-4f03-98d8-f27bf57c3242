// shared/UI/filters/examples/UserSearchFilterTest.tsx - Test component for UserSearchFilter
"use client";

import React, { useState } from 'react';
import UserSearchFilter from '../UserSearchFilter';

interface UserInfo {
  username: string;
  playerId?: number;
  userID?: number;
}

/**
 * Test component to verify UserSearchFilter functionality
 * This component demonstrates:
 * 1. Username search with 3-character minimum trigger
 * 2. Proper display of search results
 * 3. Extraction of user ID from selected result
 * 4. Passing extracted ID for API calls
 */
const UserSearchFilterTest: React.FC = () => {
  const [selectedUser, setSelectedUser] = useState<UserInfo | null>(null);
  const [apiCallData, setApiCallData] = useState<any>(null);
  const [dropdownTestResults, setDropdownTestResults] = useState<string[]>([]);
  const [isApplyClicked, setIsApplyClicked] = useState(false);

  const addTestResult = (result: string) => {
    setDropdownTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const handleUserChange = (userInfo: UserInfo | null) => {
    setSelectedUser(userInfo);

    if (userInfo) {
      addTestResult(`✅ User selected: ${userInfo.username} (ID: ${userInfo.playerId || userInfo.userID})`);

      // Simulate API call with extracted user ID
      const apiData = {
        username: userInfo.username,
        playerId: userInfo.playerId,
        userID: userInfo.userID,
        // This is what would be sent to the API
        apiPayload: {
          playerId: userInfo.playerId || userInfo.userID,
          // Other API parameters would go here
        }
      };
      setApiCallData(apiData);
    } else {
      addTestResult('❌ User selection cleared');
      setApiCallData(null);
    }
  };

  const handleApplyFilters = () => {
    setIsApplyClicked(true);
    if (selectedUser) {
      addTestResult(`🚀 Apply clicked - API call would be triggered with playerId: ${selectedUser.playerId || selectedUser.userID}`);
      // Simulate API call delay
      setTimeout(() => {
        addTestResult('✅ Simulated API call completed successfully');
        setIsApplyClicked(false);
      }, 1000);
    } else {
      addTestResult('⚠️ Apply clicked but no user selected');
      setIsApplyClicked(false);
    }
  };

  const clearTestResults = () => {
    setDropdownTestResults([]);
  };

  return (
    <div className="p-6 bg-background min-h-screen">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">UserSearchFilter Test</h1>

        <div className="bg-elevated p-6 rounded-lg mb-6">
          <h2 className="text-lg font-semibold text-white mb-4">Username Search</h2>
          <p className="text-gray-400 text-sm mb-4">
            Type at least 3 characters to search for users. The component should:
            <br />• Show loading state while searching
            <br />• Display search results with username and email
            <br />• Extract user ID when a user is selected
            <br />• Pass the ID for API calls
          </p>

          <UserSearchFilter
            value={selectedUser?.username || ''}
            onChange={handleUserChange}
            placeholder="Search for a username..."
            className="mb-4"
          />

          {/* Apply Button */}
          <div className="flex gap-3">
            <button
              onClick={handleApplyFilters}
              disabled={isApplyClicked}
              className="
                bg-golden-button hover:bg-golden-gradient
                shadow-golden-button hover:shadow-golden-button-hover
                text-white font-medium px-4 py-2 rounded-lg
                flex items-center gap-2
                transition-all duration-200
                hover:transform hover:-translate-y-0.5
                disabled:opacity-50 disabled:cursor-not-allowed
                disabled:hover:transform-none
              "
            >
              <i className="ri-search-line"></i>
              {isApplyClicked ? 'Applying...' : 'Apply Filters'}
            </button>

            <button
              onClick={clearTestResults}
              className="
                bg-gray-600 hover:bg-gray-700
                text-white font-medium px-4 py-2 rounded-lg
                flex items-center gap-2
                transition-all duration-200
              "
            >
              <i className="ri-delete-bin-line"></i>
              Clear Results
            </button>
          </div>
        </div>

        {/* Test Results */}
        {dropdownTestResults.length > 0 && (
          <div className="bg-elevated p-6 rounded-lg mb-6">
            <h3 className="text-lg font-semibold text-white mb-4">Test Results</h3>
            <div className="bg-background p-4 rounded max-h-48 overflow-y-auto">
              {dropdownTestResults.map((result, index) => (
                <div key={index} className="text-sm text-gray-300 mb-1 font-mono">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Display selected user info */}
        {selectedUser && (
          <div className="bg-elevated p-6 rounded-lg mb-6">
            <h3 className="text-lg font-semibold text-white mb-4">Selected User</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Username:</span>
                <span className="text-white">{selectedUser.username}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Player ID:</span>
                <span className="text-white">{selectedUser.playerId || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">User ID:</span>
                <span className="text-white">{selectedUser.userID || 'N/A'}</span>
              </div>
            </div>
          </div>
        )}

        {/* Display API call data */}
        {apiCallData && (
          <div className="bg-elevated p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">API Call Data</h3>
            <pre className="bg-background p-4 rounded text-sm text-gray-300 overflow-x-auto">
              {JSON.stringify(apiCallData, null, 2)}
            </pre>
            <p className="text-gray-400 text-sm mt-4">
              The playerId ({apiCallData.apiPayload.playerId}) would be sent to the API for subsequent calls.
            </p>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-section p-6 rounded-lg mt-6">
          <h3 className="text-lg font-semibold text-white mb-4">Test Instructions</h3>
          <ol className="text-gray-400 text-sm space-y-2 list-decimal list-inside">
            <li>Type at least 3 characters in the search field (e.g., "kio" for kioskuser)</li>
            <li>Wait for the API call to complete and results to appear</li>
            <li>Click on a user from the dropdown results</li>
            <li>Verify that the user information is displayed correctly</li>
            <li>Check that the API call data shows the extracted user ID</li>
            <li>Confirm that the playerId/userID can be used for subsequent API calls</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default UserSearchFilterTest;
