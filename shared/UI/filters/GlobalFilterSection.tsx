import { FilterDefinition, SpkAdvancedFilters } from "@/shared/UI/components";
import { PrimaryButton } from "@/shared/UI/components";
import React from "react";
// Performance monitoring simplified - can be re-enabled when needed
// import { useRenderTracker } from "@/shared/utils/performanceMonitoring";

/**
 * Centralized styling configuration for GlobalFilterSection
 * Allows easy customization of colors and styles from a single location
 */
export interface FilterSectionTheme {
  // Container styling
  containerBackground?: string;
  containerBorder?: string;
  containerBorderRadius?: string;

  // Header styling
  headerBackground?: string;
  headerBorder?: string;
  headerBorderRadius?: string;

  // Title styling
  titleColor?: string;
  titleFont?: string;
  titleSize?: string;
  titleWeight?: string;

  // Description styling
  descriptionColor?: string;
  descriptionFont?: string;
  descriptionSize?: string;

  // Icon styling
  iconColor?: string;
  iconSize?: string;

  // Filter label styling
  labelTextColor?: string;
  labelFont?: string;
  labelSize?: string;
  labelWeight?: string;

  // Filter input styling
  inputBackground?: string;
  inputBorder?: string;
  inputTextColor?: string;
  inputPlaceholderColor?: string;

  // Button styling
  buttonBackground?: string;
  buttonHoverBackground?: string;
  buttonTextColor?: string;
  buttonBorder?: string;

  // Dropdown styling
  dropdownBackground?: string;
  dropdownBorder?: string;
  dropdownTextColor?: string;
  dropdownHoverBackground?: string;
}

/**
 * Default dark theme configuration following established design system
 */
export const DEFAULT_FILTER_THEME: FilterSectionTheme = {
  // Container styling
  containerBackground: "bg-filter", // #1D1D1F
  containerBorder: "border-transparent",
  containerBorderRadius: "rounded-lg",

  // Header styling
  headerBackground: "bg-section", // #272729
  headerBorder: "border-filter-heading", // #333333
  headerBorderRadius: "rounded-t-lg",

  // Title styling
  titleColor: "text-white", // #FFFFFF
  titleFont: "font-rubik",
  titleSize: "text-lg",
  titleWeight: "font-medium",

  // Description styling
  descriptionColor: "text-secondary", // #AEAEAE
  descriptionFont: "font-rubik",
  descriptionSize: "text-sm",

  // Icon styling
  iconColor: "text-primary-400", // #E1B649
  iconSize: "text-lg",

  // Filter label styling
  labelTextColor: "text-secondary", // #AEAEAE
  labelFont: "font-rubik",
  labelSize: "text-sm",
  labelWeight: "font-medium",

  // Filter input styling
  inputBackground: "bg-form-input", // #2C2C2F
  inputBorder: "border-secondary", // #FFFFFF33
  inputTextColor: "text-white", // #FFFFFF
  inputPlaceholderColor: "text-tertiary", // #616161

  // Button styling
  buttonBackground: "bg-golden-button",
  buttonHoverBackground: "hover:bg-golden-gradient",
  buttonTextColor: "text-white",
  buttonBorder: "border-transparent",

  // Dropdown styling
  dropdownBackground: "bg-section", // #272729
  dropdownBorder: "border-secondary", // #FFFFFF33
  dropdownTextColor: "text-white", // #FFFFFF
  dropdownHoverBackground: "hover:bg-elevated", // #272729
};

export interface GlobalFilterSectionProps<T extends Record<string, any>> {
  filters: T;
  onFilterChange: (filters: Partial<T>) => void;
  isLoading?: boolean;
  availableFilters: FilterDefinition[];
  defaultVisibleFilters: string[];
  title?: string;
  description?: string;
  className?: string;
  showExportButton?: boolean;
  onExport?: () => void;
  showTimeSelect?: boolean;
  exportLabel?: string;
  exportIcon?: string;
  showRefreshButton?: boolean;
  onRefresh?: () => void;
  isRefreshing?: boolean;

  // New theming props
  theme?: Partial<FilterSectionTheme>;
  customStyles?: {
    container?: string;
    header?: string;
    title?: string;
    description?: string;
    icon?: string;
  };
}

/**
 * Global Filter Section Component
 *
 * Reusable filter section component for admin pages with:
 * - Horizontal layout with collapsible sections and visual indicators for active filters
 * - Dropdown-based filter selection following Jira-style filter arrangement
 * - Centralized theming system for easy customization of colors and styles
 * - Dark theme compatibility with consistent styling and layout for admin pages
 * - Optional export functionality with golden button styling
 * - Enhanced visual design with proper spacing and animations
 * - Optimized with React.memo to prevent unnecessary re-renders during pagination changes
 */
const GlobalFilterSection = React.memo(function GlobalFilterSection<T extends Record<string, any>>({
  filters,
  onFilterChange,
  isLoading = false,
  availableFilters,
  defaultVisibleFilters,
  title = "Filters",
  // description,
  className = "",
  showExportButton = false,
  onExport,
  showTimeSelect = false,
  exportLabel = "Export as CSV",
  exportIcon = "ri-download-line",
  showRefreshButton = false,
  onRefresh,
  isRefreshing = false,
  theme = {},
  customStyles = {}
}: GlobalFilterSectionProps<T>) {

  // Performance monitoring - track re-renders (disabled for now)
  // useRenderTracker('GlobalFilterSection', {
  //   filtersCount: Object.keys(filters).length,
  //   isLoading,
  //   showExportButton
  // });

  // Merge default theme with custom theme
  const mergedTheme = { ...DEFAULT_FILTER_THEME, ...theme };

  // Build dynamic class names based on theme configuration
  const containerClasses = [
    "transform transition-all duration-500 ease-in-out z-[2] relative",
    mergedTheme.containerBorderRadius,
    className,
    customStyles.container
  ].filter(Boolean).join(" ");

  const filterContainerClasses = [
    mergedTheme.containerBorderRadius,
    mergedTheme.containerBackground,
    "overflow-visible"
  ].filter(Boolean).join(" ");

  const headerClasses = [
    "p-[10px] border-b",
    mergedTheme.headerBackground,
    mergedTheme.headerBorder,
    mergedTheme.headerBorderRadius,
    customStyles.header
  ].filter(Boolean).join(" ");

  const titleClasses = [
    mergedTheme.titleColor,
    mergedTheme.titleFont,
    mergedTheme.titleSize,
    mergedTheme.titleWeight,
    customStyles.title
  ].filter(Boolean).join(" ");

  // const descriptionClasses = [
  //   mergedTheme.descriptionColor,
  //   mergedTheme.descriptionFont,
  //   mergedTheme.descriptionSize,
  //   "mt-1",
  //   customStyles.description
  // ].filter(Boolean).join(" ");

  const iconClasses = [
    "ri-filter-3-line",
    mergedTheme.iconColor,
    mergedTheme.iconSize,
    customStyles.icon
  ].filter(Boolean).join(" ");

  return (
    <div className={containerClasses}>
      <div className={filterContainerClasses}>
        {/* Enhanced Header with Export Button */}
        <div className={headerClasses}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <i className={iconClasses}></i>
              <h3 className={titleClasses}>{title}</h3>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              {/* Refresh Button */}
              {showRefreshButton && onRefresh && (
                <PrimaryButton
                  size={'sm'}
                  onClick={onRefresh}
                  disabled={isRefreshing}
                  icon={{
                    type: 'FONT_ICON',
                    iconClass: 'ri-refresh-line',
                    library: 'remix'
                  }}
                  iconPosition="left"
                >
                  {isRefreshing ? "Refreshing..." : "Refresh"}
                </PrimaryButton>
              )}

              {/* Export Button */}
              {showExportButton && onExport && (
                <PrimaryButton
                  size={'sm'}
                  onClick={onExport}
                  disabled={isLoading}
                  icon={{
                    type: 'FONT_ICON',
                    iconClass: exportIcon,
                    library: 'remix'
                  }}
                  iconPosition="left"
                >
                  {exportLabel}
                </PrimaryButton>
              )}
            </div>
          </div>

          {/* {description && (
            <div className={descriptionClasses}>
              {description}
            </div>
          )} */}
        </div>

        {/* Filter Content */}
        <SpkAdvancedFilters
          filters={filters}
          onFilterChange={onFilterChange}
          isLoading={isLoading}
          showTimeSelect={showTimeSelect}
          availableFilters={availableFilters}
          defaultVisibleFilters={defaultVisibleFilters}
          title="" // We handle the title in our header
          description="" // We handle the description in our header
          theme={mergedTheme} // Pass theme to child component
        />
      </div>
    </div>
  );
}) as <T extends Record<string, any>>(props: GlobalFilterSectionProps<T>) => React.ReactElement;

export default GlobalFilterSection;
