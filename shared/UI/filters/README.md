# GlobalFilterSection Theming System

The `GlobalFilterSection` component now includes a comprehensive theming system that allows easy customization of colors, fonts, and styles from a single location. This ensures consistent theming across all filter components while maintaining the established dark theme design system.

## Features

- **Centralized Styling**: Configure all visual aspects from one location
- **Semantic Color Classes**: Uses established Tailwind semantic classes
- **Theme Inheritance**: Child components automatically inherit theme settings
- **Predefined Themes**: Ready-to-use themes for common use cases
- **Custom Themes**: Easy creation of custom themes
- **Type Safety**: Full TypeScript support with intellisense

## Basic Usage

### Default Theme
```tsx
import { GlobalFilterSection } from "@/shared/UI/filters/GlobalFilterSection";

<GlobalFilterSection
  filters={filters}
  onFilterChange={onFilterChange}
  availableFilters={availableFilters}
  defaultVisibleFilters={defaultVisibleFilters}
  title="User Filters"
  description="Filter users by various criteria"
/>
```

### Using Predefined Themes
```tsx
import { GlobalFilterSection } from "@/shared/UI/filters/GlobalFilterSection";
import { FILTER_THEMES } from "@/shared/UI/filters/themes/filterThemes";

<GlobalFilterSection
  filters={filters}
  onFilterChange={onFilterChange}
  availableFilters={availableFilters}
  defaultVisibleFilters={defaultVisibleFilters}
  title="User Management"
  theme={FILTER_THEMES.USER_MANAGEMENT}
/>
```

### Custom Theme Configuration
```tsx
import { GlobalFilterSection } from "@/shared/UI/filters/GlobalFilterSection";

const customTheme = {
  labelTextColor: "text-blue-300",
  titleColor: "text-blue-100",
  iconColor: "text-blue-400",
  headerBackground: "bg-blue-900",
};

<GlobalFilterSection
  filters={filters}
  onFilterChange={onFilterChange}
  availableFilters={availableFilters}
  defaultVisibleFilters={defaultVisibleFilters}
  title="Custom Styled Filters"
  theme={customTheme}
/>
```

## Theme Configuration Options

### Container Styling
- `containerBackground`: Background color for the main container
- `containerBorder`: Border color for the main container
- `containerBorderRadius`: Border radius for the main container

### Header Styling
- `headerBackground`: Background color for the header section
- `headerBorder`: Border color for the header section
- `headerBorderRadius`: Border radius for the header section

### Title Styling
- `titleColor`: Text color for the title
- `titleFont`: Font family for the title
- `titleSize`: Font size for the title
- `titleWeight`: Font weight for the title

### Description Styling
- `descriptionColor`: Text color for the description
- `descriptionFont`: Font family for the description
- `descriptionSize`: Font size for the description

### Icon Styling
- `iconColor`: Color for icons
- `iconSize`: Size for icons

### Filter Label Styling
- `labelTextColor`: Text color for filter labels
- `labelFont`: Font family for filter labels
- `labelSize`: Font size for filter labels
- `labelWeight`: Font weight for filter labels

### Input Styling
- `inputBackground`: Background color for input fields
- `inputBorder`: Border color for input fields
- `inputTextColor`: Text color for input fields
- `inputPlaceholderColor`: Placeholder text color for input fields

### Button Styling
- `buttonBackground`: Background color for buttons
- `buttonHoverBackground`: Hover background color for buttons
- `buttonTextColor`: Text color for buttons
- `buttonBorder`: Border color for buttons

### Dropdown Styling
- `dropdownBackground`: Background color for dropdown menus
- `dropdownBorder`: Border color for dropdown menus
- `dropdownTextColor`: Text color for dropdown menus
- `dropdownHoverBackground`: Hover background color for dropdown items

## Predefined Themes

### DARK_THEME (Default)
The default dark theme following the established design system.

### LIGHT_LABEL_THEME
Dark theme with softer label colors for better readability.

### COMPACT_THEME
Smaller sizing for dense layouts while maintaining dark theme colors.

### HIGH_CONTRAST_THEME
Enhanced contrast for accessibility with brighter colors.

### MINIMAL_THEME
Clean, minimal appearance with reduced visual elements.

### USER_MANAGEMENT_THEME
Optimized for user management pages with purple accents.

### FINANCIAL_THEME
Optimized for financial/betting reports with golden accents.

## Advanced Usage

### Creating Custom Themes
```tsx
import { createCustomTheme, FILTER_THEMES } from "@/shared/UI/filters/themes/filterThemes";

// Extend an existing theme
const myCustomTheme = createCustomTheme(FILTER_THEMES.DARK, {
  labelTextColor: "text-green-300",
  iconColor: "text-green-400",
  titleColor: "text-green-100",
});

<GlobalFilterSection
  theme={myCustomTheme}
  // ... other props
/>
```

### Using Custom Styles
For additional customization beyond theme properties, use the `customStyles` prop:

```tsx
<GlobalFilterSection
  theme={FILTER_THEMES.DARK}
  customStyles={{
    container: "shadow-xl border-2 border-primary",
    header: "bg-gradient-to-r from-primary to-secondary",
    title: "text-2xl font-bold",
    description: "italic",
    icon: "animate-pulse"
  }}
  // ... other props
/>
```

## Migration Guide

### From Hardcoded Styles
If you have existing filter components with hardcoded styles:

1. Replace hardcoded Tailwind classes with theme properties
2. Use predefined themes or create custom themes
3. Test the visual appearance matches your requirements

### Example Migration
```tsx
// Before
<div className="bg-filter border-filter-heading">
  <h3 className="text-white font-rubik text-lg">Filters</h3>
</div>

// After
<GlobalFilterSection
  theme={{
    containerBackground: "bg-filter",
    headerBorder: "border-filter-heading",
    titleColor: "text-white",
    titleFont: "font-rubik",
    titleSize: "text-lg"
  }}
  title="Filters"
/>
```

## Best Practices

1. **Use Semantic Classes**: Prefer semantic Tailwind classes (e.g., `bg-section`) over hex values
2. **Consistent Theming**: Use the same theme across related components
3. **Theme Inheritance**: Let child components inherit theme settings automatically
4. **Performance**: Themes are merged once per render, so performance impact is minimal
5. **Accessibility**: Consider using `HIGH_CONTRAST_THEME` for better accessibility

## TypeScript Support

The theming system is fully typed with TypeScript:

```tsx
import type { FilterSectionTheme } from "@/shared/UI/filters/GlobalFilterSection";

const myTheme: FilterSectionTheme = {
  labelTextColor: "text-blue-300", // ✅ Valid
  invalidProperty: "value", // ❌ TypeScript error
};
```
