# PrimaryButton Size Variants

The PrimaryButton component now supports three new size variants in addition to the default size. All variants maintain the golden gradient theme and design system while providing different sizing options for various use cases.

## Size Variants

### Extra Small (`size="xsm"`)
- **Font**: Rubik, 500 weight, 14px size, 100% line-height
- **Padding**: 10px top/bottom, 12px left/right
- **Gap**: 8px (between icon and text)
- **Border radius**: 8px
- **Use case**: Compact interfaces, table actions, inline buttons

```tsx
<PrimaryButton size="xsm" onClick={handleClick}>
  Save
</PrimaryButton>
```

### Small (`size="sm"`)
- **Font**: Rubik, 700 weight, 14px size, 100% line-height
- **Padding**: 10px top/bottom, 24px left/right
- **Gap**: 4px (between icon and text)
- **Border radius**: 8px
- **Use case**: Secondary actions, form buttons, card actions

```tsx
<PrimaryButton size="sm" onClick={handleClick}>
  Edit User
</PrimaryButton>
```

### Large (`size="lg"`)
- **Font**: Rubik, 700 weight, 18px size, 100% line-height
- **Padding**: 12px top/bottom, 16px left/right
- **Gap**: 8px (between icon and text)
- **Border radius**: 8px
- **Use case**: Primary CTAs, hero sections, important actions

```tsx
<PrimaryButton size="lg" onClick={handleClick}>
  Get Started
</PrimaryButton>
```

### Default (no size prop)
- **Font**: Rubik, 700 weight, 18px size, 24px line-height
- **Padding**: 12px top/bottom, 16px left/right
- **Gap**: 4px (between icon and text)
- **Border radius**: 8px
- **Use case**: Standard buttons, form submissions

```tsx
<PrimaryButton onClick={handleClick}>
  Submit
</PrimaryButton>
```

## Usage Examples

### Basic Usage
```tsx
import PrimaryButton from '@/shared/UI/components/buttons/PrimaryButton';

// Different sizes
<PrimaryButton size="xsm">Extra Small</PrimaryButton>
<PrimaryButton size="sm">Small</PrimaryButton>
<PrimaryButton>Default</PrimaryButton>
<PrimaryButton size="lg">Large</PrimaryButton>
```

### With Icons
```tsx
const iconProps = {
  type: 'SVG' as const,
  url: '/assets/icons/plus.svg',
};

// Left icon
<PrimaryButton size="sm" icon={iconProps} iconPosition="left">
  Add Item
</PrimaryButton>

// Right icon
<PrimaryButton size="lg" icon={iconProps} iconPosition="right">
  Continue
</PrimaryButton>
```

### Loading States
```tsx
<PrimaryButton size="sm" loading loadingText="Saving...">
  Save Changes
</PrimaryButton>
```

### Full Width
```tsx
<PrimaryButton size="lg" fullWidth>
  Complete Purchase
</PrimaryButton>
```

### As Link
```tsx
<PrimaryButton as="link" href="/dashboard" size="sm">
  Go to Dashboard
</PrimaryButton>
```

## Icon Sizing

Icons are automatically sized based on the button size:
- **xsm/sm**: 16px icons
- **default**: 18px icons  
- **lg**: 20px icons

You can override the icon size by providing a `size` prop to the icon:

```tsx
const customIconProps = {
  type: 'SVG' as const,
  url: '/assets/icons/custom.svg',
  size: 24, // Override default sizing
};

<PrimaryButton size="sm" icon={customIconProps}>
  Custom Icon Size
</PrimaryButton>
```

## Design System Integration

All size variants:
- Use the Rubik font family (`font-rubik`)
- Maintain the golden gradient background (`bg-golden-button`)
- Include multi-layered shadows (`shadow-golden-button`)
- Support hover effects (`hover:shadow-golden-button-hover`)
- Have consistent focus states (`focus:ring-[#E1B649]/50`)
- Use 0% letter spacing for all variants
- Support dark theme compatibility

## Backward Compatibility

The component maintains full backward compatibility. Existing buttons without a `size` prop will continue to work exactly as before with the default styling.

## Migration Guide

If you're migrating from the legacy `SpkPrimaryButton` component:

```tsx
// Old (SpkPrimaryButton)
<SpkPrimaryButton size="sm">Button</SpkPrimaryButton>

// New (PrimaryButton)
<PrimaryButton size="sm">Button</PrimaryButton>
```

The new size variants provide more precise control and better consistency with the design system.
