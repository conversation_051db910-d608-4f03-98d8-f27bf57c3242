# Primary Button Component Documentation

## Overview

The `PrimaryButton` component provides standardized golden gradient button styling that exactly matches the SignInFormUI design. This component ensures visual consistency across the entire application while maintaining the exact styling specifications.

## Features

- **Exact Golden Gradient**: `linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%)`
- **Multi-layered Box Shadows**: Matching SignInFormUI exactly
- **Loading State**: With spinner animation
- **Accessibility**: Full ARIA support
- **Responsive**: Works across all screen sizes
- **Consistent Typography**: 20px font size with Rubik font family

## Usage

### Basic Usage

```tsx
import { PrimaryButton } from '@/shared/UI/components';

function MyComponent() {
  return (
    <PrimaryButton onClick={() => console.log('Clicked!')}>
      Click Me
    </PrimaryButton>
  );
}
```

### With Loading State

```tsx
<PrimaryButton
  loading={isLoading}
  loadingText="Processing..."
  disabled={isLoading}
>
  Submit Form
</PrimaryButton>
```

### Full Width Button

```tsx
<PrimaryButton fullWidth>
  Full Width Button
</PrimaryButton>
```

### Form Submit Button

```tsx
<PrimaryButton
  type="submit"
  disabled={!isFormValid}
>
  Create User
</PrimaryButton>
```

## Props Interface

```tsx
interface PrimaryButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  loading?: boolean;
  loadingText?: string;
  fullWidth?: boolean;
  id?: string;
  'aria-label'?: string;
  title?: string;
}
```

## Design System Integration

### Visual Specifications

- **Background**: Golden gradient `linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%)`
- **Box Shadow**: Multi-layered with inner highlights and outer shadows
- **Typography**: 20px font size, bold weight, Rubik font family
- **Border Radius**: 8px
- **Padding**: 16px vertical, 1rem horizontal
- **Focus Ring**: Golden ring with 50% opacity

### States

1. **Default**: Golden gradient with standard shadows
2. **Hover**: Enhanced shadows with slight upward transform
3. **Focus**: Golden focus ring for accessibility
4. **Disabled**: 70% opacity, no hover effects
5. **Loading**: Spinner animation with loading text

## Migration from Legacy Buttons

### From Inline Styles (SignInFormUI)

**Before:**
```tsx
<button
  style={{
    background: 'linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%)',
    // ... other inline styles
  }}
>
  Sign In
</button>
```

**After:**
```tsx
<PrimaryButton type="submit" loading={isPending}>
  Sign In
</PrimaryButton>
```

### From SpkButton with Custom Classes

**Before:**
```tsx
<SpkButton
  customClass="bg-blue-600 hover:bg-blue-700 text-white"
>
  Submit
</SpkButton>
```

**After:**
```tsx
<PrimaryButton>
  Submit
</PrimaryButton>
```

## Best Practices

1. **Use for Primary Actions**: Reserve for the most important action on a page/form
2. **Consistent Loading States**: Always use the `loading` prop for async operations
3. **Accessibility**: Provide `aria-label` for icon-only buttons
4. **Form Integration**: Use `type="submit"` for form submissions
5. **Disable During Loading**: Always disable the button when loading is true

## Examples in the Codebase

- **SignInFormUI**: Login form submission
- **WalletTransactionForm**: Transaction processing
- **SpkFormActions**: Form submission actions
- **UserManagementPageHeader**: Add new user action
- **HeaderSportsbook**: Sportsbook launch button

## Tailwind Classes Used

The component uses these standardized Tailwind classes:
- `bg-golden-button`: The exact golden gradient
- `shadow-golden-button`: Multi-layered shadows
- `hover:shadow-golden-button-hover`: Enhanced hover shadows
- `focus:ring-[#E1B649]/50`: Consistent focus ring

## Related Components

- **SpkPrimaryButton**: Legacy component (still supported for backward compatibility)
- **SpkFormActions**: Uses PrimaryButton for submit actions
- **SpkButton**: Generic button component for non-primary actions
