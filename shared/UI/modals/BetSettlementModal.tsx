// shared/UI/components/modal/BetSettlementModal.tsx
import React, { useState, useEffect } from 'react';
import BaseModal from './BaseModal';
import { PrimaryButton } from '../buttons';

export interface BetSettlementData {
  action: 'Settle' | 'Failed';
  comments: string;
}

interface BetSettlementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: BetSettlementData) => void;
  betId: string;
  isLoading?: boolean;
  showBackdrop?: boolean;
  className?: string;
}

/**
 * BetSettlementModal Component
 * 
 * A modal component for settling bet transactions following the established modal patterns.
 * Used when marking bets as settled or failed with required comments.
 * 
 * Features:
 * - Dimensions: 462px width × dynamic height
 * - Dark theme styling with bg-background and bg-elevated
 * - Dropdown for Settle/Failed selection
 * - Required comments field with validation
 * - Form validation and error handling
 * - Accessibility and keyboard support
 */
const BetSettlementModal: React.FC<BetSettlementModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  // betId,
  isLoading = false,
  showBackdrop = true,
  className = ''
}) => {
  const [formData, setFormData] = useState<BetSettlementData>({
    action: 'Settle',
    comments: ''
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        action: 'Settle',
        comments: ''
      });
      setErrors({});
    }
  }, [isOpen]);

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.comments.trim()) {
      newErrors.comments = 'Comments are required';
    } else if (formData.comments.trim().length < 3) {
      newErrors.comments = 'Comments must be at least 3 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    onSubmit(formData);
  };

  const handleInputChange = (field: keyof BetSettlementData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Bet Settlement"
      size="user-management"
      showBackdrop={showBackdrop}
      className={className}
      bodyClassName="p-4 space-y-4"
    >
      <form onSubmit={handleSubmit} className="space-y-4">

        <div className="text-sm text-text-secondary flex flex-col gap-2 items-center justify-center ">
          <p className="text-[20px] font-bold text-[#E1B649]">You want to settle this bet?</p>
          <p className="text-[14px] font-mono">Please select your option below</p>
        </div>

        {/* Action Dropdown */}
        <div className="flex flex-col gap-[8px]">
          <label
            className="font-rubik font-semibold text-white capitalize"
            style={{ fontSize: '14px', fontWeight: 600 }}
          >
            Settle Status <span className="text-red-500">*</span>
          </label>
          <select
            value={formData.action}
            onChange={(e) => handleInputChange('action', e.target.value as 'Settle' | 'Failed')}
            className="w-full h-[43px] bg-elevated border border-border-secondary rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent"
            disabled={isLoading}
          >
            <option value="Settle">Settle</option>
            <option value="Reject">Reject</option>
          </select>
        </div>

        {/* Comments Field */}
        <div className="flex flex-col gap-[8px]">
          <textarea
            value={formData.comments}
            onChange={(e) => handleInputChange('comments', e.target.value)}
            placeholder="Enter settlement comments..."
            rows={4}
            className={`w-full bg-elevated border rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent resize-none ${errors.comments ? 'border-red-500' : 'border-border-secondary'
              }`}
            disabled={isLoading}
          />
          {errors.comments && (
            <p className="text-red-500 text-sm">{errors.comments}</p>
          )}
        </div>

        {/* Actions */}
        <div className="pt-4">
          <PrimaryButton
            type="submit"
            disabled={isLoading}
            loading={isLoading}
            loadingText="Processing..."
            fullWidth
            size="lg"
          >
            Process
          </PrimaryButton>
        </div>
      </form>
    </BaseModal>
  );
};

export default BetSettlementModal;
