# UserManagementModal Test Documentation

## Updated Functionality Test

The UserManagementModal component has been successfully updated to handle both user activation and deactivation functionality.

## Test Cases

### 1. Deactivate User Modal
- **URL**: `?modal=user-management&mode=deactivate&userId=123`
- **Expected Behavior**:
  - Modal title: "Deactivate User"
  - Icon: Red deactivate icon
  - Message: "Do you confirm the deactivation of this user account from our platform."
  - Button text: "Deactivate User"
  - Loading text: "Deactivating..."
  - API call: `active: false`

### 2. Activate User Modal
- **URL**: `?modal=user-management&mode=activate&userId=123`
- **Expected Behavior**:
  - Modal title: "Activate User"
  - Icon: Green activate icon
  - Message: "Do you confirm the activation of this user account on our platform."
  - Button text: "Activate User"
  - Loading text: "Activating..."
  - API call: `active: true`

### 3. Table Integration
- **Active User**: Shows red deactivate button, calls `openDeactivateModal()`
- **Inactive User**: Shows green activate button, calls `openActivateModal()`

## API Integration

Both activation and deactivation use the same API endpoint:
- **Endpoint**: `POST /api/v2/cashier/player/update`
- **Payload**: Complete user data with `active` field set to `true` or `false`

## Updated Components

1. **UserManagementModal.tsx**: Added activate mode support
2. **UserManagementIcon.tsx**: Added activate icon with green theme
3. **useUserManagementModal.ts**: Added handleActivateUser function
4. **useModalNavigation.ts**: Added openActivateUserModal function
5. **UserTableColumns.tsx**: Updated button logic for activate/deactivate
6. **GlobalUserManagementModal.tsx**: Added activate mode handling

## Testing Instructions

1. Navigate to user management page
2. Find an active user and click the deactivate button
3. Verify modal shows deactivation content
4. Find an inactive user and click the activate button
5. Verify modal shows activation content
6. Test API calls complete successfully
7. Verify user status updates in the table after modal closes

## Success Criteria

- ✅ Modal dynamically adapts UI based on activate/deactivate mode
- ✅ API calls send correct `active` boolean value
- ✅ Button text and loading states are context-appropriate
- ✅ Icons and colors match the action type
- ✅ Table buttons show correct action based on user status
- ✅ URL parameters support both modes
- ✅ All existing functionality remains intact
