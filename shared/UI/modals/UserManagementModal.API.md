# User Management Modal API Integration

## Updated Modal Specifications

The User Management Modal has been updated with the following specifications:

### Input Fields (in order)
1. **Username** (required field with validation)
2. **Name** (optional text input - replaces firstName/lastName)
3. **Mobile Number** (optional phone number input)
4. **Password** (required for new user, optional for edit with "Change Password" toggle)

### Toggle Switches
- **Kiosk Access Toggle** (with computer icon `ri-computer-line`)
- **Website Access Toggle** (with globe icon `ri-global-line`)
- **Active state background**: #E1B649 (golden theme color)
- **Inactive state**: gray background with white thumb

### Modal Container Styling
- **Fixed width**: 462px
- **Dynamic height**: grows with content, shows scroll when needed
- **Content padding**: 12px top, 16px right, 12px bottom, 16px left
- **Gap between elements**: 16px
- **Background**: bg-elevated following dark theme system

### UI Changes
- ✅ Removed Cancel buttons from modal footer
- ✅ Relies on close icon (X) in modal header for dismissal
- ✅ Single action button (Create/Update) spans full width

## API Payload Structure

### Create User Payload

```typescript
interface CreateUserPayload {
  userName: string;           // Required - from Username field
  firstName: string;          // Derived from Name field (first word)
  lastName: string;           // Derived from Name field (remaining words)
  phone: string;              // From Mobile Number field
  userType: number;           // Calculated from access toggles
  encryptedPassword: string;  // Base64 encoded password
  
  // Default/system fields (maintained for API compatibility)
  nickName: string;           // Empty string
  email: string;              // Empty string (removed from UI)
  phoneCode: string;          // Default '+94'
  zipCode: string;            // Empty string
  dateOfBirth: string;        // Empty string
  countryCode: string;        // Default 'LK'
  currencyId: number;         // Default 1
  activeBonusId: null;        // Default null
  vipLevel: number;           // Default 1
  city: string;               // Empty string
  emailVerified: boolean;     // Default false
  phoneVerified: boolean;     // Default false
  forceResetPassword: boolean; // Default false
  markAsBot: boolean;         // Default false
  active: boolean;            // Default true
  demo: boolean;              // Default false
  affiliatedData: string;     // Empty string
  nationalId: null;           // Default null
  clickId: null;              // Default null
  wyntaClickId: null;         // Default null
  categoryType: null;         // Default null
}
```

### Edit User Payload

```typescript
interface EditUserPayload extends CreateUserPayload {
  id: number;                 // User ID
  encryptedPassword?: string; // Optional - only if changing password
}
```

### UserType Mapping

The `userType` field is calculated based on the toggle switches:

```typescript
const calculateUserType = (): number => {
  if (kioskAccess && websiteAccess) return 3; // Kiosk & Online
  if (kioskAccess) return 2;                  // Kiosk only
  return 1;                                   // Online only (default)
};
```

**UserType Values:**
- `1` = "Online" (website access only)
- `2` = "Kiosk" (kiosk access only)
- `3` = "Kiosk & Online" (both access types)

### Name Field Processing

The single "Name" field is split into firstName and lastName:

```typescript
const splitName = (fullName: string): { firstName: string; lastName: string } => {
  const nameParts = fullName.trim().split(' ');
  if (nameParts.length === 1) {
    return { firstName: nameParts[0], lastName: '' };
  }
  const firstName = nameParts[0];
  const lastName = nameParts.slice(1).join(' ');
  return { firstName, lastName };
};
```

### Password Handling

- **Create Mode**: Password is required and must be at least 6 characters
- **Edit Mode**: Password is optional with "Change Password" toggle
- **Encryption**: All passwords are base64 encoded before sending to API

```typescript
// Password encryption
import { encodePassword } from '@/shared/utils/passwordEncryption';
const encryptedPassword = encodePassword(password);
```

## API Endpoints

- **Create User**: `POST /api/v2/cashier/player/add`
- **Edit User**: Uses existing edit user mutation endpoint

## Validation Rules

- **Username**: Required for create mode
- **Name**: Optional field, no validation
- **Mobile Number**: Optional field, no validation  
- **Password**: 
  - Required for create mode
  - Required when "Change Password" is enabled in edit mode
  - Minimum 6 characters when provided

## Component Architecture

- **AccessToggleSwitch**: Reusable toggle component in `shared/UI/components/`
- **UserManagementModalForm**: Updated form component with new field structure
- **UserManagementModal**: Updated container with dynamic height
- **Business Logic**: Extracted into `useUserManagementModal` hook
