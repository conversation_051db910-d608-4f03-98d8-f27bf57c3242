# UserManagementModal System

A comprehensive modal system for user management operations (create, edit, deactivate) that provides a unified modal experience following the dark theme design system.

## Overview

The UserManagementModal system provides a reusable modal component that handles all user management operations through URL parameters, offering a seamless user experience with shareable URLs and global accessibility.

## Features

### 🎨 **Design Specifications**
- **Dimensions**: 462px width × 518px height
- **Styling**: 8px border-radius, `bg-background` background
- **Theme**: Full dark theme compatibility with `bg-elevated` sections
- **Typography**: Rubik font family with specified weights and sizes

### 🔧 **Three Operation Modes**
1. **Create Mode**: New user creation with form validation
2. **Edit Mode**: User editing with pre-populated data and disabled username
3. **Deactivate Mode**: Confirmation dialog with custom styling

### ♿ **Accessibility & UX**
- Keyboard support (ESC key to close)
- Proper ARIA attributes and roles
- Loading states and error handling
- Form validation with real-time feedback

## Components

### UserManagementModal

Main modal component that handles the three operation modes.

```tsx
import { UserManagementModal } from '@/shared/UI/components/modal';

<UserManagementModal
  isOpen={true}
  onClose={handleClose}
  mode="create" // 'create' | 'edit' | 'deactivate'
  userData={userData} // Required for edit/deactivate modes
  onSubmit={handleSubmit}
  onSuccess={handleSuccess}
  isLoading={isLoading}
/>
```

#### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `isOpen` | `boolean` | ✅ | Whether the modal is visible |
| `onClose` | `() => void` | ✅ | Callback when modal is closed |
| `mode` | `UserManagementModalMode` | ✅ | Operation mode: 'create', 'edit', or 'deactivate' |
| `userData` | `UserDetailsData` | ❌ | User data for edit/deactivate modes |
| `onSubmit` | `(formData) => Promise<void>` | ❌ | Form submission handler |
| `onSuccess` | `() => void` | ❌ | Success callback |
| `isLoading` | `boolean` | ❌ | Loading state |
| `showBackdrop` | `boolean` | ❌ | Show backdrop overlay (default: true) |
| `className` | `string` | ❌ | Additional CSS classes |

### UserManagementModalForm

Form component specifically designed for the modal with essential fields and validation.

```tsx
import UserManagementModalForm from './UserManagementModalForm';

<UserManagementModalForm
  mode="create"
  userData={userData}
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  isLoading={isLoading}
/>
```

### useUserManagementModal Hook

Custom hook that encapsulates all business logic for modal operations.

```tsx
import { useUserManagementModal } from '@/shared/hooks/business/useUserManagementModal';

const {
  isModalOpen,
  modalMode,
  selectedUser,
  openCreateModal,
  openEditModal,
  openDeactivateModal,
  closeModal,
  handleCreateUser,
  handleEditUser,
  handleDeactivateUser,
  isCreating,
  isEditing,
  isDeactivating,
} = useUserManagementModal({
  onSuccess: () => {
    // Handle success
  }
});
```

## Integration

### URL Parameter System

The system uses URL parameters for modal control, making URLs shareable and bookmarkable:

- **Create**: `?modal=user-management&mode=create`
- **Edit**: `?modal=user-management&mode=edit&userId=123`
- **Deactivate**: `?modal=user-management&mode=deactivate&userId=123`

### Global Modal Integration

The modal is rendered globally through the ClientProviders component:

```tsx
// app/Clientprovider.tsx
import { GlobalUserManagementModal } from "@/shared/UI/components/modal";

export default function ClientProviders({ children }: ClientProvidersProps) {
  return (
    <QueryProvider>
      {/* ... other providers */}
      {children}

      {/* Global Modal System */}
      <GlobalUserManagementModal />
    </QueryProvider>
  );
}
```

### Table Actions Integration

User table actions have been updated to use URL parameters:

```tsx
// Using URL parameters for modal control
<button
  onClick={() => handleModalClick(`?modal=user-management&mode=edit&userId=${record.id}`)}
  className="flex items-center gap-2 px-3 py-2 text-sm text-text-primary hover:bg-surface transition-colors w-full text-left"
>
  Edit
</button>
```

## Form Fields

### Create/Edit Mode Fields

- **Username** (required for create, disabled for edit)
- **First Name** (required)
- **Last Name** (required)
- **Email** (required, validated)
- **Password** (required for create, optional for edit)
- **Status** (active/inactive checkbox)

### Validation Rules

- Username: Required for create mode
- Email: Required and must be valid email format
- Password: Required for create, minimum 6 characters
- First/Last Name: Required fields

### Deactivate Mode

- Confirmation dialog with specific styling
- Golden title (#E1B649): "Are you sure?"
- Subtitle (#D4D4D4): Confirmation message
- Two buttons: Cancel (PrimaryButton) and Confirm (custom styled)

## Styling Specifications

### Header Section
- Background: `bg-elevated`
- Border: `border-b border-border-secondary`
- Padding: 12px top/bottom, 16px left/right
- Title: Rubik font, 700 weight, 20px size, 100% line-height

### Content Section
- Padding: 12px top/bottom, 16px left/right
- Gap: 16px between elements
- Height: 446px (total modal height minus header)

### Form Elements
- Labels: Rubik font, 600 weight, 14px size, white color, capitalize
- Inputs: `bg-elevated` background, `border-border-secondary` border
- Gap: 8px between label and input, 12px between form groups

### Deactivate Mode
- Content gap: 20px
- Title: #E1B649 color, Rubik 900 weight, 20px, center-aligned
- Subtitle: #D4D4D4 color, Rubik 500 weight, 16px, center-aligned
- Confirm button: 189.5px × 53px, #FFFFFF33 background, 18px font

## API Integration

The modal integrates with existing user management API endpoints:

- **Create**: Uses `useCreateUserMutation` hook
- **Edit**: Uses `useEditUserMutation` hook
- **Deactivate**: Custom implementation (placeholder for future API)

### Password Encryption

Passwords are encrypted using the existing `encodePassword` utility before submission.

### Query Cache Management

The system automatically invalidates relevant queries after successful operations:

- User list queries (`['userList']`)
- Specific user details queries (`['userDetails', userId]`)

## Testing

A test component is available for manual testing:

```tsx
import { UserManagementModalTest } from '@/shared/UI/components/modal/UserManagementModal.test';

// Use in development for testing modal functionality
<UserManagementModalTest />
```

## Migration Notes

### Breaking Changes
- User table edit links now point to `/user-management/edit/[id]` instead of `/user-management/edit/[id]`
- Create user button now navigates to `/user-management/create` instead of `/user-management/create`
- Deactivate actions now use modal routes instead of inline buttons

### Backward Compatibility
- Existing API endpoints remain unchanged
- Form validation logic is preserved
- User data structures are maintained
- All existing functionality is preserved in modal format

## Future Enhancements

- Sound notifications for successful operations
- Bulk user operations modal
- Advanced user role management
- User avatar upload functionality
- Enhanced validation with server-side checks
