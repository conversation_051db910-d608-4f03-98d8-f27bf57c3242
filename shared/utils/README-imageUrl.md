# Centralized Image URL Utility

This document explains how to use the centralized image URL utility for handling backend image URLs throughout the application.

## Overview

The `buildImageUrl` utility function provides consistent image URL construction across the application, handling various edge cases and ensuring all backend images are loaded correctly.

## Environment Variables

The utility uses the following environment variables in order of preference:

1. `NEXT_PUBLIC_IMAGES_BASE_URL` - Primary image base URL (e.g., `https://assets.roylfc.com`)
2. `NEXT_PUBLIC_STAGING_BACKEND_URL` - Fallback base URL (e.g., `https://staging-reports-api.8dexsuperadmin.com`)

## Usage

### Basic Usage

```typescript
import { buildImageUrl } from '@/shared/utils/imageOptimization';

// Backend returns relative path
const avatarUrl = buildImageUrl(userData.avatarImage);
// Returns: 'https://assets.roylfc.com/uploads/avatars/user123.jpg'

// Backend returns absolute URL
const profileUrl = buildImageUrl('https://example.com/image.jpg');
// Returns: 'https://example.com/image.jpg' (unchanged)

// Backend returns null/undefined
const defaultUrl = buildImageUrl(null);
// Returns: '/images/profile.png' (default fallback)

// Custom fallback
const customUrl = buildImageUrl(null, '/images/custom-avatar.png');
// Returns: '/images/custom-avatar.png'
```

### With React Components

```typescript
import { buildImageUrl, isExternalImage } from '@/shared/utils/imageOptimization';
import Image from 'next/image';

const UserAvatar = ({ userData }) => {
  const avatarUrl = buildImageUrl(userData.avatarImage);
  const isDefaultFallback = avatarUrl === '/images/profile.png';
  
  return !isDefaultFallback ? (
    <Image
      src={avatarUrl}
      alt="User Avatar"
      width={80}
      height={80}
      className="rounded-full"
      unoptimized={isExternalImage(avatarUrl)}
    />
  ) : (
    <div className="avatar-fallback">
      {userData.id.charAt(0).toUpperCase()}
    </div>
  );
};
```

### With OptimizedImage Component

```typescript
import { AvatarImage } from '@/shared/components/OptimizedImage';

// AvatarImage automatically uses buildImageUrl
const UserProfile = ({ userData }) => (
  <AvatarImage
    src={userData.avatarImage}
    alt="User Avatar"
    size="lg"
    fallbackSrc="/images/custom-fallback.png"
  />
);

// Or use OptimizedImage with useBackendUrl flag
import OptimizedImage from '@/shared/components/OptimizedImage';

const ProfilePicture = ({ userData }) => (
  <OptimizedImage
    src={userData.avatarImage}
    alt="Profile Picture"
    useBackendUrl={true}
    fallbackSrc="/images/default-profile.png"
  />
);
```

## Edge Cases Handled

1. **Null/Undefined Image Paths**: Returns fallback image
2. **Empty String**: Returns fallback image
3. **Absolute URLs**: Returns unchanged (no base URL prepended)
4. **Relative Paths**: Prepends appropriate base URL
5. **Invalid URLs**: Returns fallback image with console warning
6. **Missing Environment Variables**: Returns fallback with console warning

## Migration Guide

### Before (Manual URL Construction)

```typescript
// Old approach - inconsistent and error-prone
function getAvatarUrl(avatarImage: string | null | undefined): string | null {
  if (!avatarImage) return null;
  
  if (isValidUrl(avatarImage)) {
    return avatarImage;
  }
  
  const baseUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL || "";
  return `${baseUrl}/${avatarImage}`;
}

const avatarUrl = getAvatarUrl(userData.avatarImage);
```

### After (Centralized Utility)

```typescript
// New approach - consistent and robust
import { buildImageUrl } from '@/shared/utils/imageOptimization';

const avatarUrl = buildImageUrl(userData.avatarImage);
```

## Benefits

1. **Consistency**: All image URLs are constructed the same way
2. **Maintainability**: Single place to update image URL logic
3. **Error Handling**: Robust handling of edge cases
4. **Fallbacks**: Automatic fallback to default images
5. **Environment Flexibility**: Supports multiple base URL configurations
6. **Type Safety**: Full TypeScript support with proper types

## Next.js Configuration

The utility automatically works with Next.js Image optimization. Make sure your `next.config.ts` includes the image domains:

```typescript
module.exports = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "assets.roylfc.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https", 
        hostname: "staging-reports-api.8dexsuperadmin.com",
        port: "",
        pathname: "/**",
      },
    ],
  },
};
```
