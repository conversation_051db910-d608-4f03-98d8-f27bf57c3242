// shared/utils/currencyUtils.ts
// Utility functions for currency formatting and validation

/**
 * Currency ID to ISO code mapping
 * Add more mappings as needed based on your system's currency IDs
 */
const CURRENCY_ID_MAP: { [key: string]: string } = {
  '1': 'USD',
  '2': 'EUR',
  '3': 'GBP',
  '4': 'CAD',
  '5': 'AUD',
  '6': 'JPY',
  '7': 'CHF',
  '8': 'CNY',
  '9': 'INR',
  '10': 'USD', // Based on the error, ID 10 should map to USD
  '11': 'SGD',
  '12': 'HKD',
  '13': 'NZD',
  '14': 'SEK',
  '15': 'NOK',
  '16': 'DKK',
  '17': 'PLN',
  '18': 'CZK',
  '19': 'HUF',
  '20': 'RUB',
  // Add more mappings as needed
};

/**
 * Validates if a string is a valid ISO 4217 currency code
 * @param currency - The currency code to validate
 * @returns boolean indicating if the currency code is valid
 */
export const isValidCurrencyCode = (currency: string): boolean => {
  if (!currency || typeof currency !== 'string' || currency.length !== 3) {
    return false;
  }

  try {
    // Test if the currency code is valid by creating a temporary formatter
    new Intl.NumberFormat('en-US', { style: 'currency', currency: currency });
    return true;
  } catch (error) {
    //eslint-disable-next-line no-console
    console.error('Failed to validate currency code:', error);
    return false;
  }
};

/**
 * Converts currency ID (number) or validates currency code (string) to a valid ISO currency code
 * @param currency - Currency ID (number) or ISO code (string)
 * @returns Valid ISO currency code or 'USD' as fallback
 */
export const getCurrencyCode = (currency: string | number | undefined | null): string => {
  // Handle null/undefined cases
  if (currency === null || currency === undefined) {
    return 'USD';
  }

  // Handle string currency codes
  if (typeof currency === 'string') {
    // If it's a 3-letter string, validate it as ISO code
    if (currency.length === 3 && isValidCurrencyCode(currency.toUpperCase())) {
      return currency.toUpperCase();
    }

    // If it's a numeric string, treat it as currency ID
    if (/^\d+$/.test(currency)) {
      return CURRENCY_ID_MAP[currency] || 'USD';
    }

    // Invalid string format, fallback to USD
    return 'USD';
  }

  // Handle numeric currency IDs
  if (typeof currency === 'number') {
    return CURRENCY_ID_MAP[currency.toString()] || 'USD';
  }

  // Fallback for any other type
  return 'USD';
};

/**
 * Formats a number as currency with proper validation and fallback handling
 * @param amount - The amount to format
 * @param currency - Currency ID (number) or ISO code (string)
 * @param locale - Locale for formatting (default: 'en-US')
 * @param options - Additional Intl.NumberFormat options
 * @returns Formatted currency string
 */
export const formatCurrency = (
  amount: number | string | undefined | null,
  currency: string | number | undefined | null = 'USD',
  locale: string = 'en-US',
  options: Partial<Intl.NumberFormatOptions> = {}
): string => {
  // Handle null/undefined amounts
  if (amount === null || amount === undefined) {
    return formatCurrency(0, currency, locale, options);
  }

  // Convert string amounts to numbers
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  // Handle invalid numbers
  if (isNaN(numAmount)) {
    return formatCurrency(0, currency, locale, options);
  }

  // Get valid currency code
  const currencyCode = getCurrencyCode(currency);

  // Default formatting options
  const defaultOptions: Intl.NumberFormatOptions = {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    ...options
  };

  try {
    return new Intl.NumberFormat(locale, defaultOptions).format(numAmount);
  } catch (error) {
    //eslint-disable-next-line no-console
    console.error('Failed to format currency:', error);
    // Fallback to simple formatting if Intl.NumberFormat fails
    return `$${numAmount.toFixed(2)}`;
  }
};

/**
 * Formats currency without the currency symbol (just the number)
 * @param amount - The amount to format
 * @param currency - Currency ID (number) or ISO code (string) - used for decimal places
 * @param locale - Locale for formatting (default: 'en-US')
 * @returns Formatted number string
 */
export const formatCurrencyAmount = (
  amount: number | string | undefined | null,
  currency: string | number | undefined | null = 'USD',
  locale: string = 'en-US'
): string => {
  // Handle null/undefined amounts
  if (amount === null || amount === undefined) {
    return '0.00';
  }

  // Convert string amounts to numbers
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

  // Handle invalid numbers
  if (isNaN(numAmount)) {
    return '0.00';
  }

  // Get currency code to determine decimal places
  const currencyCode = getCurrencyCode(currency);

  // Some currencies have different decimal place conventions
  const decimalPlaces = ['JPY', 'KRW', 'VND'].includes(currencyCode) ? 0 : 2;

  try {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
    }).format(numAmount);
  } catch (error) {
    //eslint-disable-next-line no-console
    console.error('Failed to format currency amount:', error);
    return numAmount.toFixed(decimalPlaces);
  }
};

/**
 * Gets the currency symbol for a given currency code
 * @param currency - Currency ID (number) or ISO code (string)
 * @param locale - Locale for formatting (default: 'en-US')
 * @returns Currency symbol
 */
export const getCurrencySymbol = (
  currency: string | number | undefined | null = 'USD',
  locale: string = 'en-US'
): string => {
  const currencyCode = getCurrencyCode(currency);

  try {
    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    // Format 0 and extract just the symbol
    const formatted = formatter.format(0);
    return formatted.replace(/[\d\s,]/g, '');
  } catch (error) {
    //eslint-disable-next-line no-console
    console.error('Failed to get currency symbol:', error);
    // Fallback to common currency symbols
    const symbolMap: { [key: string]: string } = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'CNY': '¥',
      'INR': '₹',
      'CAD': 'C$',
      'AUD': 'A$',
    };

    return symbolMap[currencyCode] || '$';
  }
};

/**
 * Add a new currency ID mapping
 * @param id - Currency ID
 * @param code - ISO currency code
 */
export const addCurrencyMapping = (id: string | number, code: string): void => {
  if (isValidCurrencyCode(code)) {
    CURRENCY_ID_MAP[id.toString()] = code.toUpperCase();
  }
};

/**
 * Get all available currency mappings
 * @returns Object with currency ID to ISO code mappings
 */
export const getCurrencyMappings = (): { [key: string]: string } => {
  return { ...CURRENCY_ID_MAP };
};
