// shared/components/tables/CashierReportTableColumns.tsx
import { CopyToClipboard, SpkTableColumn } from "@/shared/UI/components";
import { ActionTypeBadge, StatusBadge } from "@/shared/UI/components";
import { CashierTransaction } from "@/shared/types/report-types";
import { formatCurrency } from "@/shared/utils/currencyUtils";

// Helper function to format date
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Status and action type styling is now handled by the StatusBadge and ActionTypeBadge components

export const getCashierReportTableColumns = (): SpkTableColumn[] => {
  return [
    {
      key: "userName",
      title: "Username",
      sortable: true,
      width: "140px",
      render: (value, record: CashierTransaction) => (
        <div className="flex items-center gap-2">
          <span className="font-medium text-text-muted text-sm truncate">
            {value || record.userName || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "actionType",
      title: "Action Type",
      sortable: true,
      width: "120px",
      render: (value, record: CashierTransaction) => {
        const actionType = value || record.actionType || "N/A";
        return <ActionTypeBadge actionType={actionType} className="truncate w-[120px]" />;
      }
    },
    {
      key: "marketId",
      title: "Market ID",
      sortable: true,
      width: "120px",
      render: (value, record: CashierTransaction) => {
        const marketId = value || record.marketId || "N/A";
        return (
          <div className="flex items-center gap-2">
            <span className="font-mono text-text-muted text-sm truncate">
              {marketId}
            </span>
            {marketId !== "N/A" && (
              <CopyToClipboard
                text={marketId}
                iconSize={14}
                className="opacity-60 hover:opacity-100"
              />
            )}
          </div>
        );
      }
    },
    {
      key: "marketName",
      title: "Market Name",
      sortable: true,
      width: "180px",
      render: (value, record: CashierTransaction) => (
        <div className="text-sm">
          <span className="text-text-muted truncate block">
            {value || record.marketName || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "amount",
      title: "Amount",
      sortable: true,
      width: "120px",
      render: (value, record: CashierTransaction) => (
        <div className="text-sm">
          <span className="font-medium text-text-muted truncate block">
            {formatCurrency(value || record.amount, record.currency)}
          </span>
        </div>
      )
    },
    {
      key: "createdAt",
      title: "Transaction Timestamp",
      sortable: true,
      width: "180px",
      render: (value, record: CashierTransaction) => (
        <div className="text-sm">
          <span className="text-text-muted truncate block">
            {formatDate(value || record.createdAt)}
          </span>
        </div>
      )
    },
    {
      key: "transactionId",
      title: "Transaction / Bet ID",
      sortable: true,
      width: "160px",
      render: (value, record: CashierTransaction) => {
        const transactionId = value || record.transactionId || "N/A";
        return (
          <div className="flex items-center gap-2">
            <span className="font-mono text-sm text-text-muted truncate">
              {transactionId}
            </span>
            {transactionId !== "N/A" && (
              <CopyToClipboard
                text={transactionId}
                iconSize={14}
                className="opacity-60 hover:opacity-100"
              />
            )}
          </div>
        );
      }
    },
    {
      key: "description",
      title: "Description",
      width: "200px",
      render: (value, record: CashierTransaction) => (
        <div className="text-sm">
          <span className="text-text-secondary truncate block">
            {value || record.description || "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "status",
      title: "Status",
      sortable: true,
      width: "100px",
      render: (value, record: CashierTransaction) => {
        const status = value || record.status || "unknown";
        return <StatusBadge status={status} />;
      }
    }
  ];
};
