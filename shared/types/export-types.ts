// shared/types/export-types.ts - TypeScript interfaces for CSV export system

// Export request payload interfaces
export interface ExportRequestPayload {
  payload: string; // JSON stringified filter parameters
  module: string;
  type: string;
}

// Export Center API request interface
export interface ExportCenterFilters {
  size: number;
  page: number;
  search_id: string;
  status: string;
  sort_by: string;
  order: 'asc' | 'desc';
}

// Export Center response interfaces
export interface ExportCenterItem {
  admin_id: number;
  admin_type: string;
  allFilesPath: string[];
  created_at: string;
  created_at_gmt_formatted: string;
  csv_url: string;
  email_sent: string | null;
  id: number;
  isMultiple: boolean;
  payload: string;
  progress_bar: string;
  status: number;
  type: string;
  updated_at: string;
}

export interface ExportCenterResponse {
  success: number;
  message: string;
  record: {
    data: ExportCenterItem[];
    first_page_url: string;
    from: number;
    last_page: number;
    last_page_url: string;
    links: Array<any>;
    next_page_url: null;
    path: string;
    per_page: number;
    prev_page_url: null;
    to: number;
    total: number;

  };

}

// Export request response interface
export interface ExportRequestResponse {
  success: number;
  message: string;
  data?: {
    export_id: string;
    status: string;
  };
  errors: string[];
}

// Export module types - Limited to 3 supported report types
export type ExportModuleType =
  | 'casino_transactions'
  | 'bet_report'
  | 'financial_report';

export type ExportDataType =
  | 'casino_transactions_db'
  | 'cashier_bet_report';

// Export status color mapping
export const EXPORT_STATUS_COLORS = {
  pending: 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20',
  processing: 'text-blue-400 bg-blue-400/10 border-blue-400/20',
  completed: 'text-green-400 bg-green-400/10 border-green-400/20',
  failed: 'text-red-400 bg-red-400/10 border-red-400/20'
} as const;

// Export status icons
export const EXPORT_STATUS_ICONS = {
  pending: 'ri-time-line',
  processing: 'ri-loader-4-line',
  completed: 'ri-check-circle-line',
  failed: 'ri-error-warning-line'
} as const;

// Default export center filters
export const DEFAULT_EXPORT_CENTER_FILTERS: ExportCenterFilters = {
  size: 10,
  page: 1,
  // eslint-disable-next-line camelcase
  search_id: '',
  status: '',
  // eslint-disable-next-line camelcase
  sort_by: 'id',
  order: 'desc'
};

// Export type display names - Only supported report types
export const EXPORT_TYPE_DISPLAY_NAMES = {
  // eslint-disable-next-line camelcase
  casino_transactions_db: 'Casino Transactions',
  // eslint-disable-next-line camelcase
  cashier_bet_report: 'Bet Report',
  // eslint-disable-next-line camelcase
  financial_report: 'Financial Report'
} as const;

// Export module display names - Only supported report types
export const EXPORT_MODULE_DISPLAY_NAMES = {
  // eslint-disable-next-line camelcase
  casino_transactions: 'Casino Transactions',
  // eslint-disable-next-line camelcase
  bet_report: 'Bet Report',
  // eslint-disable-next-line camelcase
  financial_report: 'Financial Report'
} as const;
