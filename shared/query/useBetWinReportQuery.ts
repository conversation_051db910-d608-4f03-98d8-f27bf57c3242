// shared/query/useBetWinReportQuery.ts
import { useAuthStore } from '@/shared/stores/authStore';
import {
  BetReportFilters,
  BetReportResponse,
  DEFAULT_BET_REPORT_FILTERS
} from '@/shared/types/report-types';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { useQuery } from '@tanstack/react-query';

/**
 * Extract market information from betList array
 */
const extractMarketInfoFromBetList = (betList: any[]): { marketId?: string; marketName?: string } => {
  if (!betList || !Array.isArray(betList) || betList.length === 0) {
    return {};
  }

  const firstBet = betList[0];
  return {
    marketId: firstBet.market_id,
    marketName: firstBet.market
  };
};

/**
 * Calculate combined odds for combo bets
 */
const calculateCombinedOdds = (betList: any[]): number => {
  if (!betList || !Array.isArray(betList) || betList.length === 0) {
    return 0;
  }

  // For combo bets, multiply all odds together
  return betList.reduce((total, bet) => total * (bet.price || 1), 1);
};

/**
 * Build request body for bet win report API
 */
const buildBetReportRequestBody = (filters: BetReportFilters): any => {
  // Use dateRange if available, otherwise fall back to individual startDate/endDate
  const startDate = filters.dateRange?.startDate || filters.startDate;
  const endDate = filters.dateRange?.endDate || filters.endDate;

  const requestBody: any = {
    page: filters.page.toString(),
    limit: filters.limit.toString(),
    startDate: startDate,
    endDate: endDate
  };

  // Add optional fields only if they have values
  if (filters.transactionId) {
    requestBody.transactionId = filters.transactionId;
  }

  if (filters.playerId) {
    requestBody.playerId = filters.playerId;
  }

  if (filters.marketId) {
    requestBody.marketId = filters.marketId;
  }

  if (filters.status) {
    requestBody.status = filters.status;
  }

  if (filters.payoutStatus) {
    requestBody.payoutStatus = filters.payoutStatus;
  }

  return requestBody;
};

/**
 * Fetch bet win report data from the new reporting API
 * Uses POST method with JSON body as specified in the API documentation
 */
export const fetchBetWinReport = async (filters: BetReportFilters): Promise<BetReportResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Use the reporting backend URL from environment variables
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('Reporting backend URL is not configured');
  }

  // Build request body
  const requestBody = buildBetReportRequestBody(filters);

  const response = await fetch(`${baseUrl}/api/v2/cashier/betWinReport`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(requestBody),
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch bet report: ${response.status}`);
  }

  const apiResponse = await response.json();

  // Transform data to match BetReportData interface
  const rawData = apiResponse.data || [];
  const transformedData = rawData.map((bet: any) => {
    const marketInfo = extractMarketInfoFromBetList(bet.betList);
    const combinedOdds = calculateCombinedOdds(bet.betList);

    return {
      id: bet.betSlipId,
      betId: bet.betId,
      transactionId: bet.betId, // Using betId as transactionId
      userName: bet.userName,
      playerName: bet.userName, // Same as userName
      betAmount: parseFloat(bet.betAmount) || 0,
      winAmount: parseFloat(bet.winAmount) || 0,
      status: bet.status || 'unknown',
      createdAt: bet.createdAt,
      marketId: marketInfo.marketId,
      marketName: marketInfo.marketName,
      betType: bet.betType || 'unknown',
      odds: combinedOdds,
      payoutStatus: bet.payoutStatus,
      currency: 'USD' // Default currency, can be updated based on API response
    };
  });

  // Calculate totals from the transformed data
  const totalBetAmount = transformedData.reduce((sum: number, bet: any) => sum + (bet.betAmount || 0), 0);
  const totalWinAmount = transformedData.reduce((sum: number, bet: any) => sum + (bet.winAmount || 0), 0);
  const ggr = totalBetAmount - totalWinAmount;

  return {
    data: transformedData,
    success: apiResponse.success,
    message: apiResponse.message || '',
    errors: apiResponse.errors || [],
    totalBetAmount,
    totalWinAmount,
    ggr,
    count: parseInt(apiResponse.count) || transformedData.length,
    totalPages: Math.ceil((parseInt(apiResponse.count) || transformedData.length) / filters.limit),
    currentPage: filters.page
  };
};

export const useBetWinReportQuery = (filters: Partial<BetReportFilters> = {}, initialData?: BetReportResponse | null) => {
  const { token } = useAuthStore();

  // Build final filters with defaults
  const finalFilters: BetReportFilters = {
    ...DEFAULT_BET_REPORT_FILTERS,
    ...filters,
  };

  return useQuery<BetReportResponse>({
    queryKey: ['betWinReport', finalFilters],
    queryFn: async () => {
      try {
        return await fetchBetWinReport(finalFilters);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token, // Only run query if user is authenticated
    staleTime: 2 * 60 * 1000, // Data considered fresh for 2 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    initialData: initialData || undefined,
  });
};

// Hook for refetching bet win report with new filters
export const useBetWinReportRefetch = () => {
  return (filters: Partial<BetReportFilters> = {}) => {
    const finalFilters: BetReportFilters = {
      ...DEFAULT_BET_REPORT_FILTERS,
      ...filters,
    };

    return fetchBetWinReport(finalFilters);
  };
};
