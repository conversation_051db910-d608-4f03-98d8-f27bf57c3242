// shared/query/mutations/useCreateUserMutation.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { getStagingBackendUrl } from '@/shared/utils/envValidation';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';

export interface CreateUserData {
  userName: string;
  nickName?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  zipCode: string;
  dateOfBirth: string;
  countryCode: string;
  phoneCode: string;
  currencyId: number;
  activeBonusId?: number | null;
  vipLevel: number;
  city: string;
  emailVerified: boolean;
  phoneVerified: boolean;
  forceResetPassword: boolean;
  markAsBot: boolean;
  active: boolean;
  demo: boolean;
  affiliatedData?: string;
  nationalId?: string | null;
  clickId?: string | null;
  wyntaClickId?: string | null;
  categoryType?: string | null;
  encryptedPassword: string;
  userType: number;
}

export interface EditUserData extends Omit<CreateUserData, 'encryptedPassword'> {
  id: number;
  encryptedPassword?: string; // Optional for edit - only required if changing password
}

export interface CreateUserResponse {
  data: any;
  errors: string[];
  success: number;
  message: string;
}

const createUser = async (userData: CreateUserData): Promise<CreateUserResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Get validated staging backend URL
  const baseUrl = getStagingBackendUrl();

  const response = await fetch(`${baseUrl}/api/v2/cashier/player/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(userData),
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to create user: ${response.status}`);
  }

  return response.json();
};

export const useCreateUserMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<CreateUserResponse, Error, CreateUserData>({
    mutationFn: createUser,
    onSuccess: (data) => {
      if (data.success === 1) {
        // Invalidate user list queries to refresh the data
        queryClient.invalidateQueries({ queryKey: ['userList'] });
      } else {
        throw new Error(data.message || 'User creation failed with unexpected response');
      }
    },
    onError: (error) => {
      handleQueryError(error); // Handle 401 errors globally
    },
  });
};
