// shared/query/useLoginHistoryQuery.ts
import { useAuthStore } from '@/shared/stores/authStore';
import {
  DEFAULT_LOGIN_HISTORY_FILTERS,
  LoginHistoryApiResponse,
  LoginHistoryFilters,
  LoginHistoryResponse
} from '@/shared/types/user-management-types';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { useQuery } from '@tanstack/react-query';

/**
 * Fetch login history data from the reporting backend
 * 
 * @param filters - Login history filters
 * @returns Formatted login history response
 */
export const fetchLoginHistory = async (filters: LoginHistoryFilters): Promise<LoginHistoryResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Use the reporting API URL
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || 'https://reporting.ingrandstation.com';

  if (!baseUrl) {
    throw new Error('Reporting API base URL is not configured');
  }

  // Prepare API payload - exclude dateRange as API expects startDate/endDate directly
  const apiPayload = {
    page: filters.page,
    limit: filters.limit,
    startDate: filters.startDate,
    endDate: filters.endDate,
    ...(filters.playerId && { playerId: filters.playerId })
  };

  const response = await fetch(`${baseUrl}/api/v2/cashier/palyer/login-history`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(apiPayload),
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch login history: ${response.status}`);
  }

  const apiResponse: LoginHistoryApiResponse = await response.json();

  // Transform the API response to match our expected format
  const formattedResponse: LoginHistoryResponse = {
    data: apiResponse.data.rows,
    count: apiResponse.data.count,
    success: apiResponse.success,
    message: apiResponse.message,
    errors: apiResponse.errors,
    // Calculate total pages based on count and limit
    totalPages: Math.ceil(apiResponse.data.count / parseInt(filters.limit)),
    // Current page from filters
    currentPage: parseInt(filters.page)
  };

  return formattedResponse;
};

/**
 * Custom hook for fetching login history data
 * 
 * @param filters - Login history filters
 * @param initialData - Optional initial data for SSR
 * @returns Query result with login history data
 */
export const useLoginHistoryQuery = (
  filters: Partial<LoginHistoryFilters> = {},
  initialData?: LoginHistoryResponse | null
) => {
  const { token } = useAuthStore();

  // Merge default filters with provided filters
  const finalFilters: LoginHistoryFilters = {
    ...DEFAULT_LOGIN_HISTORY_FILTERS,
    ...filters
  };

  return useQuery<LoginHistoryResponse>({
    queryKey: ['loginHistory', finalFilters],
    queryFn: async () => {
      try {
        return await fetchLoginHistory(finalFilters);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token,
    initialData: initialData || undefined,
    staleTime: 30000, // 30 seconds
    retry: 1,
    refetchOnWindowFocus: false,
  });
};
