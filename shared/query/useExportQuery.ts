// shared/query/useExportQuery.ts - API functions and hooks for CSV export system

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import {
  ExportRequestPayload,
  ExportRequestResponse,
  ExportCenterFilters,
  ExportCenterResponse,
  DEFAULT_EXPORT_CENTER_FILTERS
} from '@/shared/types/export-types';

// Submit export request
export const submitExportRequest = async (payload: ExportRequestPayload): Promise<ExportRequestResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('API base URL is not configured');
  }

  // Validate payload
  if (!payload.module || !payload.type || !payload.payload) {
    throw new Error('Invalid export payload: module, type, and payload are required');
  }

  const data = JSON.stringify(payload);

  const response = await fetch(`${baseUrl}/api/admin/exportCsvCenter/store`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: data,
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to submit export request: ${response.status}`);
  }

  return response.json();
};

// Fetch export center data
export const fetchExportCenter = async (filters: ExportCenterFilters): Promise<ExportCenterResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('API base URL is not configured');
  }

  const response = await fetch(`${baseUrl}/api/admin/exportCsvCenter`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(filters),
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch export center data: ${response.status}`);
  }

  return response.json();
};

// Hook for submitting export requests
export const useExportRequestMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: submitExportRequest,
    onSuccess: () => {
      // Invalidate export center queries to refresh the list
      queryClient.invalidateQueries({ queryKey: ['exportCenter'] });
    },
    onError: (error) => {
      handleQueryError(error);
    },
  });
};

// Hook for fetching export center data
export const useExportCenterQuery = (filters: ExportCenterFilters = DEFAULT_EXPORT_CENTER_FILTERS) => {
  const token = useAuthStore((state) => state.token);

  // Ensure filters have default values
  const finalFilters = {
    ...DEFAULT_EXPORT_CENTER_FILTERS,
    ...filters
  };

  return useQuery<ExportCenterResponse>({
    queryKey: ['exportCenter', finalFilters],
    queryFn: async () => {
      try {
        return await fetchExportCenter(finalFilters);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token, // Only run query if user is authenticated
    staleTime: 30 * 1000, // Data considered fresh for 30 seconds (frequent updates for status)
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for refetching export center data
export const useExportCenterRefetch = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({ queryKey: ['exportCenter'] });
  };
};

// Download CSV file
export const downloadCsvFile = async (downloadUrl: string, fileName: string) => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  try {
    const response = await fetch(downloadUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.status}`);
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName || 'export.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    // Log error for debugging in development
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('Download failed:', error);
    }
    throw error;
  }
};
