# SEO Optimization System for Next.js App Router

This comprehensive SEO system provides server-side rendering (SSR) optimized SEO capabilities for Next.js applications using the App Router. It replaces the legacy client-side SEO component with modern, performance-focused solutions.

## Features

- ✅ **Server-Side Rendering**: All meta tags are generated on the server for better SEO
- ✅ **Structured Data (JSON-LD)**: Schema.org markup for enhanced search engine understanding
- ✅ **Dynamic Metadata**: Page-specific and dynamic route metadata generation
- ✅ **Canonical URLs**: Proper canonical URL handling
- ✅ **Sitemap Generation**: Automatic sitemap.xml generation
- ✅ **Robots.txt**: Dynamic robots.txt with environment-specific rules
- ✅ **PWA Manifest**: Progressive Web App manifest generation
- ✅ **Performance Optimized**: Zero client-side JavaScript for SEO
- ✅ **TypeScript Support**: Full TypeScript interfaces and type safety

## Quick Start

### 1. Basic Page Metadata

For static pages, export metadata directly:

```typescript
// app/dashboard/page.tsx
import type { Metadata } from "next";
import { generateDashboardMetadata } from "@/shared/seo";

export const metadata: Metadata = generateDashboardMetadata();

export default function Dashboard() {
  return <div>Dashboard content</div>;
}
```

### 2. Dynamic Page Metadata

For dynamic routes, use `generateMetadata`:

```typescript
// app/user/[id]/page.tsx
import type { Metadata } from "next";
import { generateUserManagementMetadata } from "@/shared/seo";

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const user = await fetchUser(params.id);
  
  return generateUserManagementMetadata('details', {
    title: `User Profile - ${user.name}`,
    description: `Comprehensive profile for ${user.name}`,
    path: `/user/${params.id}`,
  });
}

export default function UserPage({ params }: { params: { id: string } }) {
  return <div>User content</div>;
}
```

### 3. Structured Data

Add structured data to your pages:

```typescript
// app/dashboard/page.tsx
import { BreadcrumbStructuredData, SoftwareApplicationStructuredData } from "@/shared/seo";

export default function Dashboard() {
  return (
    <>
      <BreadcrumbStructuredData
        breadcrumbs={[
          { name: 'Home', url: '/' },
          { name: 'Dashboard' }
        ]}
      />
      <SoftwareApplicationStructuredData
        name="Dashboard"
        description="Business analytics dashboard"
        url="/dashboard"
        applicationCategory="BusinessApplication"
      />
      
      <div>Dashboard content</div>
    </>
  );
}
```

## API Reference

### Metadata Generation Functions

#### `generatePageMetadata(config: Partial<SEOConfig>): Metadata`
Generate metadata for any page with custom configuration.

#### `generateDynamicMetadata(baseConfig, dynamicData): Metadata`
Generate metadata for dynamic pages with runtime data.

#### `generateUserManagementMetadata(pageType, dynamicData?): Metadata`
Generate metadata specifically for user management pages.

#### `generateAuthMetadata(pageType): Metadata`
Generate metadata for authentication pages.

#### `generateErrorMetadata(errorType): Metadata`
Generate metadata for error pages.

### Structured Data Components

#### `<StructuredData data={jsonLdData} />`
Generic structured data component for any JSON-LD data.

#### `<OrganizationStructuredData {...props} />`
Organization schema markup.

#### `<WebsiteStructuredData {...props} />`
Website schema markup.

#### `<BreadcrumbStructuredData breadcrumbs={[...]} />`
Breadcrumb navigation schema markup.

#### `<ArticleStructuredData {...props} />`
Article schema markup for content pages.

#### `<ProductStructuredData {...props} />`
Product schema markup for e-commerce.

#### `<SoftwareApplicationStructuredData {...props} />`
Software application schema markup.

### Utility Functions

#### `createCanonicalUrl(path: string): string`
Generate canonical URLs with proper base URL.

#### `validateSEO(config): { isValid: boolean, issues: string[] }`
Validate SEO configuration for common issues.

#### `generateCompleteSEO(config): { metadata, structuredData }`
Generate both metadata and structured data for a page.

## Configuration

### Site Configuration

Update `shared/seo/config.ts` with your site details:

```typescript
export const siteConfig: SiteConfig = {
  name: "Your Site Name",
  description: "Your site description",
  url: "https://yoursite.com",
  ogImage: "/og-image.png",
  // ... other config
};
```

### Environment-Specific Settings

The system automatically adjusts based on `NODE_ENV`:

- **Development**: Disallows all crawling
- **Staging**: Disallows all crawling
- **Production**: Allows crawling with full SEO optimization

## Migration from Legacy SEO Component

### Before (Client-side)
```typescript
import Seo from "@/shared/layouts-components/seo/seo";

function Page() {
  return (
    <>
      <Seo 
        title="Page Title"
        description="Page description"
        keywords="keyword1, keyword2"
      />
      <div>Content</div>
    </>
  );
}
```

### After (Server-side)
```typescript
import type { Metadata } from "next";
import { createPageMetadata } from "@/shared/seo";

export const metadata: Metadata = createPageMetadata(
  "Page Title",
  "Page description",
  "/page-path",
  { keywords: ["keyword1", "keyword2"] }
);

function Page() {
  return <div>Content</div>;
}
```

## Best Practices

### 1. Use Server Components for SEO
Always generate metadata in server components for better SEO performance.

### 2. Include Structured Data
Add relevant structured data to improve search engine understanding.

### 3. Optimize Meta Descriptions
Keep descriptions between 120-160 characters for optimal display.

### 4. Use Canonical URLs
Always specify canonical URLs to prevent duplicate content issues.

### 5. Validate SEO Configuration
Use the `validateSEO` utility to check for common SEO issues.

## Performance Impact

- **Zero Client-Side JavaScript**: All SEO metadata is rendered server-side
- **Optimized Bundle Size**: No client-side SEO dependencies
- **Faster Page Loads**: Meta tags available immediately when page loads
- **Better Core Web Vitals**: Reduced JavaScript execution time

## Troubleshooting

### Common Issues

1. **Metadata not appearing**: Ensure you're using server components and exporting metadata correctly.

2. **Structured data errors**: Validate JSON-LD using Google's Structured Data Testing Tool.

3. **Canonical URL issues**: Check that `NEXT_PUBLIC_SITE_URL` environment variable is set correctly.

4. **Development vs Production**: Remember that robots.txt disallows crawling in development.

### Debugging

Use the browser's "View Source" to verify that meta tags are present in the initial HTML response, not added by JavaScript.

## Examples

See the following files for complete examples:
- `app/(components)/(content-layout)/dashboard/page.tsx` - Basic metadata
- `app/(components)/(content-layout)/user-management/details/[id]/layout.tsx` - Dynamic metadata
- `app/layout.tsx` - Root layout with global SEO

## Support

For questions or issues with the SEO system, refer to the Next.js App Router documentation or create an issue in the project repository.
