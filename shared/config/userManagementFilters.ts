// shared/config/userManagementFilters.ts - Filter configuration for user management
import { FilterDefinition } from "@/shared/UI/components";

// Default visible filters for user management (Primary Filters - always visible)
export const DEFAULT_USER_VISIBLE_FILTERS = ["username", "phone", "userType", "status"];

// All available filters for user management
export const USER_MANAGEMENT_FILTERS: FilterDefinition[] = [
	// PRIMARY FILTERS (always visible)
	{
		id: "username",
		label: "Username",
		icon: "ri-user-line",
		component: "userSearch",
		placeholder: "Search username...",
		filterKey: "username"
	},
	{
		id: "phone",
		label: "Phone Number",
		icon: "ri-phone-line",
		component: "input",
		placeholder: "Search by phone...",
		filterKey: "phone"
	},
	{
		id: "userType",
		label: "User Type",
		icon: "ri-user-settings-line",
		component: "select",
		filterKey: "userType",
		options: [
			{ value: null, label: "All User Types" },
			{ value: 2, label: "Kiosk" },
			{ value: 1, label: "Online" },
			{ value: 3, label: "Kiosk & Online" }
		]
	},
	{
		id: "status",
		label: "Status",
		icon: "ri-toggle-line",
		component: "select",
		filterKey: "status",
		options: [
			{ value: "", label: "All Status" },
			{ value: "true", label: "Active" },
			{ value: "false", label: "Inactive" }
		]
	},

	// SECONDARY FILTERS (inside "More Filters" collapsible section)
	{
		id: "lastLogin",
		label: "Last Login",
		icon: "ri-time-line",
		component: "dateRange",
		filterKey: "lastLogin"
	},
	{
		id: "balance",
		label: "Balance",
		icon: "ri-money-dollar-circle-line",
		component: "numberRange",
		filterKey: "balance"
	},
	{
		id: "name",
		label: "Name",
		icon: "ri-user-2-line",
		component: "input",
		placeholder: "Search by name...",
		filterKey: "name"
	}
];
