#!/usr/bin/env node

/**
 * <PERSON>ript to generate PNG favicon files from SVG
 * This script creates all the required favicon files for the application
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// SVG template for different sizes
function createSVG(size) {
  return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1D1D1D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0F0F0F;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" fill="url(#bg)" rx="${Math.max(1, size / 16)}"/>
  <rect x="${Math.max(1, size / 32)}" y="${Math.max(1, size / 32)}" width="${size - Math.max(2, size / 16)}" height="${size - Math.max(2, size / 16)}" fill="none" stroke="#E1B649" stroke-width="${Math.max(1, size / 32)}" rx="${Math.max(1, size / 32)}"/>
  <text x="${size / 2}" y="${size * 0.7}" font-family="Arial, sans-serif" font-size="${size * 0.6}" font-weight="bold" text-anchor="middle" fill="#E1B649">X</text>
</svg>`;
}

// Note: createPlaceholderPNG function was removed as it was unused
// If needed in the future, it can be implemented using a proper image library

// Files to generate
const faviconFiles = [
  { name: 'favicon-16x16.png', size: 16 },
  { name: 'favicon-32x32.png', size: 32 },
  { name: 'apple-touch-icon.png', size: 180 },
  { name: 'android-chrome-192x192.png', size: 192 },
  { name: 'android-chrome-512x512.png', size: 512 }
];

// Create public directory if it doesn't exist
const publicDir = path.join(__dirname, '..', 'public');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// eslint-disable-next-line no-console
console.log('Generating favicon files...');

// Generate SVG files for each size (these can be converted to PNG manually or with tools)
faviconFiles.forEach(({ name, size }) => {
  const svgContent = createSVG(size);
  const svgFileName = name.replace('.png', '.svg');
  const svgPath = path.join(publicDir, svgFileName);

  fs.writeFileSync(svgPath, svgContent);
  // eslint-disable-next-line no-console
  console.log(`Generated ${svgFileName}`);
});

// Create a simple ICO file (placeholder)
const icoPath = path.join(publicDir, 'favicon.ico');
const ico16 = createSVG(16);
fs.writeFileSync(icoPath, ico16);
// eslint-disable-next-line no-console
console.log('Generated favicon.ico');

// eslint-disable-next-line no-console
console.log('\nFavicon generation complete!');
// eslint-disable-next-line no-console
console.log('\nTo convert SVG files to PNG, you can use:');
// eslint-disable-next-line no-console
console.log('1. Online tools like https://convertio.co/svg-png/');
// eslint-disable-next-line no-console
console.log('2. Command line tools like ImageMagick or Inkscape'); // cspell:disable-line
// eslint-disable-next-line no-console
console.log('3. Node.js libraries like sharp or canvas');

// eslint-disable-next-line no-console
console.log('\nExample with ImageMagick:');
faviconFiles.forEach(({ name, size }) => {
  const svgName = name.replace('.png', '.svg');
  // eslint-disable-next-line no-console
  console.log(`convert public/${svgName} -resize ${size}x${size} public/${name}`);
});
