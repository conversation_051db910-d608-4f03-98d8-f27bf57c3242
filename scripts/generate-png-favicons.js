#!/usr/bin/env node

/**
 * <PERSON>rip<PERSON> to generate PNG favicon files using Sharp
 * This script creates all the required favicon files for the application
 */

import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Files to generate
const faviconFiles = [
  { name: 'favicon-16x16.png', size: 16 },
  { name: 'favicon-32x32.png', size: 32 },
  { name: 'apple-touch-icon.png', size: 180 },
  { name: 'android-chrome-192x192.png', size: 192 },
  { name: 'android-chrome-512x512.png', size: 512 }
];

// Create public directory if it doesn't exist
const publicDir = path.join(__dirname, '..', 'public');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

async function generateFavicons() { // cspell:disable-line
  // eslint-disable-next-line no-console
  console.log('Generating PNG favicon files with Sharp...');

  for (const { name, size } of faviconFiles) {
    try {
      // Create a simple favicon using Sharp
      const buffer = await sharp({
        create: {
          width: size,
          height: size,
          channels: 4,
          background: { r: 29, g: 29, b: 29, alpha: 1 } // #1D1D1D
        }
      })
      .composite([
        {
          input: Buffer.from(`
            <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
              <rect x="${Math.max(1, size / 32)}" y="${Math.max(1, size / 32)}" 
                    width="${size - Math.max(2, size / 16)}" height="${size - Math.max(2, size / 16)}" 
                    fill="none" stroke="#E1B649" stroke-width="${Math.max(1, size / 32)}" 
                    rx="${Math.max(1, size / 32)}"/>
              <text x="${size / 2}" y="${size * 0.7}" 
                    font-family="Arial, sans-serif" font-size="${size * 0.6}" 
                    font-weight="bold" text-anchor="middle" fill="#E1B649">X</text>
            </svg>
          `),
          top: 0,
          left: 0
        }
      ])
      .png()
      .toBuffer();

      const filePath = path.join(publicDir, name);
      fs.writeFileSync(filePath, buffer);
      // eslint-disable-next-line no-console
      console.log(`Generated ${name} (${size}x${size})`);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Error generating ${name}:`, error.message);
    }
  }

  // Generate favicon.ico (using 32x32 as base)
  try {
    const icoBuffer = await sharp({
      create: {
        width: 32,
        height: 32,
        channels: 4,
        background: { r: 29, g: 29, b: 29, alpha: 1 }
      }
    })
    .composite([
      {
        input: Buffer.from(`
          <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
            <rect x="1" y="1" width="30" height="30" fill="none" stroke="#E1B649" stroke-width="1" rx="1"/>
            <text x="16" y="22" font-family="Arial, sans-serif" font-size="18" font-weight="bold" text-anchor="middle" fill="#E1B649">X</text>
          </svg>
        `),
        top: 0,
        left: 0
      }
    ])
    .png()
    .toBuffer();

    const icoPath = path.join(publicDir, 'favicon.ico');
    fs.writeFileSync(icoPath, icoBuffer);
    // eslint-disable-next-line no-console
    console.log('Generated favicon.ico');
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error generating favicon.ico:', error.message);
  }

  // eslint-disable-next-line no-console
  console.log('\nFavicon generation complete!');
}

// Run the generation
// eslint-disable-next-line no-console
generateFavicons().catch(console.error); // cspell:disable-line
