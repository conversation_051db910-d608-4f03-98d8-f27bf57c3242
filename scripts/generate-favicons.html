<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .favicon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .favicon-canvas {
            border: 1px solid #ddd;
            margin-bottom: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Favicon Generator for Xintra</h1>
        <p>This tool generates the required favicon files for the application.</p>
        
        <div id="previews"></div>
        
        <button onclick="generateAllFavicons()">Generate All Favicons</button>
        <button onclick="downloadAll()">Download All</button>
        
        <div id="downloads"></div>
    </div>

    <script>
        const sizes = [
            { name: 'favicon-16x16.png', size: 16 },
            { name: 'favicon-32x32.png', size: 32 },
            { name: 'apple-touch-icon.png', size: 180 },
            { name: 'android-chrome-192x192.png', size: 192 },
            { name: 'android-chrome-512x512.png', size: 512 }
        ];

        const canvases = {};
        const downloadLinks = {};

        function createFavicon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            canvas.className = 'favicon-canvas';
            
            const ctx = canvas.getContext('2d');
            
            // Background gradient (dark theme)
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#1D1D1D');
            gradient.addColorStop(1, '#0F0F0F');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Golden accent border
            ctx.strokeStyle = '#E1B649';
            ctx.lineWidth = Math.max(1, size / 32);
            ctx.strokeRect(ctx.lineWidth/2, ctx.lineWidth/2, size - ctx.lineWidth, size - ctx.lineWidth);
            
            // Letter "X" in the center
            ctx.fillStyle = '#E1B649';
            ctx.font = `bold ${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('X', size / 2, size / 2);
            
            return canvas;
        }

        function generateAllFavicons() {
            const previewsDiv = document.getElementById('previews');
            const downloadsDiv = document.getElementById('downloads');
            
            previewsDiv.innerHTML = '';
            downloadsDiv.innerHTML = '';
            
            sizes.forEach(({ name, size }) => {
                const canvas = createFavicon(size);
                canvases[name] = canvas;
                
                // Create preview
                const preview = document.createElement('div');
                preview.className = 'favicon-preview';
                preview.innerHTML = `
                    <div>${canvas.outerHTML}</div>
                    <div>${name} (${size}x${size})</div>
                `;
                previewsDiv.appendChild(preview);
                
                // Create download link
                canvas.toBlob(blob => {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = name;
                    link.textContent = `Download ${name}`;
                    link.style.display = 'block';
                    link.style.margin = '5px 0';
                    downloadsDiv.appendChild(link);
                    downloadLinks[name] = link;
                });
            });
        }

        function downloadAll() {
            Object.values(downloadLinks).forEach(link => {
                if (link) {
                    link.click();
                }
            });
        }

        // Generate favicons on page load
        window.onload = generateAllFavicons;
    </script>
</body>
</html>
